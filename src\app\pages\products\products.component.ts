import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { CartService, CartItem } from '../../services/cart.service';

export interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  category: string;
  price: number;
  images: string[];
  artist: string;
  dimensions: string;
  materials: string[];
  featured: boolean;
}

@Component({
  selector: 'app-products',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './products.component.html',
  styleUrls: ['./products.component.css']
})
export class ProductsComponent implements OnInit {
  products: Product[] = [
    {
      id: '1',
      name: 'Traditional Mithila Painting - Madhubani Art',
      slug: 'traditional-mithila-painting-madhubani-art',
      description: 'Authentic hand-painted Mithila artwork featuring traditional motifs and vibrant colors on handmade paper.',
      category: 'Paintings',
      price: 2500,
      images: [
        'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
        'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg'
      ],
      artist: 'Sita Devi',
      dimensions: '16" x 12"',
      materials: ['Handmade Paper', 'Natural Pigments', 'Bamboo Brush'],
      featured: true
    },
    {
      id: '2',
      name: 'Decorative Clay Pot with Mithila Motifs',
      slug: 'decorative-clay-pot-mithila-motifs',
      description: 'Handcrafted clay pot adorned with traditional Mithila designs, perfect for home decoration.',
      category: 'Clay Crafts',
      price: 1200,
      images: [
        'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
        'https://i.etsystatic.com/43638819/r/il/9b1cc7/5978434663/il_794xN.5978434663_hk13.jpg'
      ],
      artist: 'Ram Babu Yadav',
      dimensions: '8" height x 6" diameter',
      materials: ['Natural Clay', 'Natural Colors', 'Traditional Tools'],
      featured: false
    },
    {
      id: '3',
      name: 'Mithila Art Saree',
      slug: 'mithila-art-saree',
      description: 'Beautiful cotton saree with hand-painted Mithila designs, blending tradition with contemporary fashion.',
      category: 'Textiles',
      price: 4500,
      images: [
        'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg'
      ],
      artist: 'Kamala Devi',
      dimensions: '6 yards',
      materials: ['Pure Cotton', 'Natural Dyes', 'Hand Painting'],
      featured: true
    },
    {
      id: '4',
      name: 'Wooden Wall Art with Mithila Carvings',
      slug: 'wooden-wall-art-mithila-carvings',
      description: 'Intricately carved wooden wall piece featuring traditional Mithila patterns and symbols.',
      category: 'Wood Crafts',
      price: 3200,
      images: [
        'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg'
      ],
      artist: 'Manoj Kumar',
      dimensions: '18" x 14" x 2"',
      materials: ['Teak Wood', 'Natural Finish', 'Hand Carving'],
      featured: false
    },
    {
      id: '5',
      name: 'Mithila Art Canvas Painting - Fish Motif',
      slug: 'mithila-canvas-painting-fish-motif',
      description: 'Large canvas painting featuring the sacred fish motif, a symbol of prosperity in Mithila culture.',
      category: 'Paintings',
      price: 5500,
      images: [
        'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg'
      ],
      artist: 'Ganga Devi',
      dimensions: '24" x 18"',
      materials: ['Canvas', 'Acrylic Colors', 'Traditional Brushes'],
      featured: true
    },
    {
      id: '6',
      name: 'Handwoven Mithila Table Runner',
      slug: 'handwoven-mithila-table-runner',
      description: 'Elegant table runner with woven Mithila patterns, perfect for dining room decoration.',
      category: 'Textiles',
      price: 1800,
      images: [
        'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg'
      ],
      artist: 'Sunita Kumari',
      dimensions: '72" x 14"',
      materials: ['Cotton Thread', 'Natural Dyes', 'Hand Weaving'],
      featured: false
    }
  ];

  filteredProducts: Product[] = [];
  selectedCategory: string = 'All';
  categories: string[] = ['All', 'Paintings', 'Clay Crafts', 'Textiles', 'Wood Crafts'];

  constructor(private cartService: CartService) {}

  ngOnInit() {
    this.filteredProducts = this.products;
  }

  filterByCategory(category: string) {
    this.selectedCategory = category;
    if (category === 'All') {
      this.filteredProducts = this.products;
    } else {
      this.filteredProducts = this.products.filter(product => product.category === category);
    }
  }

  addToCart(product: Product, event: Event) {
    event.preventDefault();
    event.stopPropagation();

    const cartItem: Omit<CartItem, 'quantity'> = {
      id: product.id,
      name: product.name,
      slug: product.slug,
      price: product.price,
      image: product.images[0],
      artist: product.artist
    };

    this.cartService.addToCart(cartItem, 1);
  }

  isInCart(productId: string): boolean {
    return this.cartService.isInCart(productId);
  }

  formatPrice(price: number): string {
    return `NPR ${price.toLocaleString()}`;
  }
}
