<!-- Artist <PERSON> Banner with <PERSON><PERSON><PERSON> Art Elements -->
<div *ngIf="artist" class="relative h-[60vh] md:h-[70vh] overflow-hidden">
  <!-- Background Image with Parallax Effect -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat transform transition-transform duration-10000 hover:scale-105"
       [style.background-image]="'url(' + artist.image + ')'">
  </div>

  <!-- Dark Overlay -->
  <div class="absolute inset-0 bg-black bg-opacity-60"></div>

  <!-- Decorative Border -->
  <app-mithila-border
    [primaryColor]="'#C1440E'"
    [secondaryColor]="'#F4B400'"
    [type]="'full'"
    position="top-8 left-8 right-8 bottom-8">
  </app-mithila-border>

  <!-- Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <!-- Decorative Element -->
    <app-mithila-decorative-element
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      [type]="'lotus'"
      position="relative mb-6"
      classes="opacity-90 animate-float-slow"
      size="80px">
    </app-mithila-decorative-element>

    <!-- Artist Name with Animation -->
    <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 font-heading animate-fade-in">
      {{artist.name}}
    </h1>

    <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
      {{artist.role}}
    </p>

    <!-- Artist Badges -->
    <div class="flex flex-wrap justify-center gap-4 mb-8">
      <div *ngIf="artist.featured" class="bg-primary-600 text-white px-4 py-2 rounded-full text-sm font-bold">
        Featured Artist
      </div>
      <div *ngIf="artist.workshops" class="bg-secondary-600 text-white px-4 py-2 rounded-full text-sm font-bold">
        Offers Workshops
      </div>
      <div class="bg-accent-600 text-white px-4 py-2 rounded-full text-sm font-bold">
        {{artist.category | titlecase}} Style
      </div>
    </div>
  </div>
</div>

<!-- Artist Bio Section -->
<app-mithila-section
  primaryColor="#C1440E"
  secondaryColor="#F4B400"
  backgroundGradient="from-primary-50 via-background-light to-secondary-50"
  backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <div *ngIf="artist" class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
    <div>
      <div class="relative rounded-lg overflow-hidden group">
        <!-- Decorative Border -->
        <div class="absolute -inset-1 bg-gradient-to-br from-primary-300 via-secondary-300 to-accent-300 rounded-lg blur-sm animate-border-gradient"></div>

        <div class="relative rounded-lg overflow-hidden">
          <img [src]="artist.image" [alt]="artist.name" class="w-full h-auto transition-transform duration-700 group-hover:scale-105">

          <!-- Overlay Gradient -->
          <div class="absolute inset-0 bg-gradient-to-t from-primary-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </div>
      </div>
    </div>

    <div class="relative">
      <!-- Decorative Element -->
      <app-mithila-decorative-element
        [primaryColor]="'#C1440E'"
        [secondaryColor]="'#F4B400'"
        [type]="'geometric'"
        position="absolute -top-4 -left-4"
        classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"
        size="40px">
      </app-mithila-decorative-element>

      <h2 class="text-3xl font-bold mb-6 text-gray-900">About {{artist.name}}</h2>

      <div class="prose prose-lg max-w-none">
        <p class="mb-4">{{artist.longBio}}</p>

        <blockquote class="italic border-l-4 border-primary-500 pl-4 my-6">
          "{{artist.quote}}"
        </blockquote>

        <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-xl font-semibold mb-3 text-gray-800">Specialization</h3>
            <p>{{artist.specialization}}</p>
          </div>

          <div>
            <h3 class="text-xl font-semibold mb-3 text-gray-800">Education</h3>
            <p>{{artist.education}}</p>
          </div>

          <div>
            <h3 class="text-xl font-semibold mb-3 text-gray-800">Experience</h3>
            <p>{{artist.experience}}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Artist Achievements Section -->
<app-mithila-section
  primaryColor="#264653"
  secondaryColor="#F4B400"
  backgroundGradient="from-secondary-50 via-background-light to-accent-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Achievements & Exhibitions"
    subtitle="Recognitions and showcases of artistic excellence"
  ></app-section-title>

  <div *ngIf="artist" class="grid grid-cols-1 lg:grid-cols-2 gap-12">
    <!-- Awards -->
    <div class="bg-white/80 backdrop-blur-sm rounded-lg p-8 shadow-lg hover:shadow-xl transition-all duration-300">
      <div class="flex items-center mb-6">
        <app-mithila-decorative-element
          [primaryColor]="'#C1440E'"
          [secondaryColor]="'#F4B400'"
          [type]="'geometric'"
          position="relative mr-4"
          classes="opacity-90"
          size="40px">
        </app-mithila-decorative-element>
        <h3 class="text-2xl font-bold">Awards & Recognition</h3>
      </div>

      <ul class="space-y-4">
        <li *ngFor="let award of artist.awards" class="flex items-start">
          <span class="inline-block w-2 h-2 rounded-full bg-primary-500 mt-2 mr-3"></span>
          <span>{{award}}</span>
        </li>
      </ul>
    </div>

    <!-- Exhibitions -->
    <div class="bg-white/80 backdrop-blur-sm rounded-lg p-8 shadow-lg hover:shadow-xl transition-all duration-300">
      <div class="flex items-center mb-6">
        <app-mithila-decorative-element
          [primaryColor]="'#C1440E'"
          [secondaryColor]="'#F4B400'"
          [type]="'geometric'"
          position="relative mr-4"
          classes="opacity-90"
          size="40px">
        </app-mithila-decorative-element>
        <h3 class="text-2xl font-bold">Exhibitions</h3>
      </div>

      <ul class="space-y-4">
        <li *ngFor="let exhibition of artist.exhibitions" class="flex items-start">
          <span class="inline-block w-2 h-2 rounded-full bg-primary-500 mt-2 mr-3"></span>
          <span>{{exhibition}}</span>
        </li>
      </ul>
    </div>
  </div>
</app-mithila-section>

<!-- Artist's Artworks Section -->
<app-mithila-section
  primaryColor="#F4B400"
  secondaryColor="#264653"
  backgroundGradient="from-primary-50 via-background-light to-secondary-50"
  backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Featured Artworks"
    subtitle="Explore the artistic creations by {{artist?.name}}"
  ></app-section-title>

  <!-- Artwork Grid -->
  <div *ngIf="artistArtworks.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
    <div *ngFor="let artwork of artistArtworks" class="bg-white/80 backdrop-blur-sm rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
      <div class="relative overflow-hidden">
        <!-- Artwork Image -->
        <img [src]="artwork.image" [alt]="artwork.title" class="w-full h-64 object-cover object-center transition-transform duration-700 group-hover:scale-110">

        <!-- Overlay Gradient -->
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>

      <div class="p-6">
        <h3 class="text-xl font-bold mb-2 text-gray-900">{{artwork.title}}</h3>
        <p class="text-gray-600 mb-4">{{artwork.description | slice:0:100}}...</p>

        <div class="flex justify-between items-center">
          <span class="text-primary-600 font-semibold">${{artwork.price}}</span>
          <a href="#" class="inline-block px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-300">
            View Details
          </a>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="artistArtworks.length === 0" class="text-center py-12">
    <p class="text-lg text-gray-600">No artworks available for this artist at the moment.</p>
  </div>
</app-mithila-section>

<!-- Artist's Workshops Section -->
<app-mithila-section *ngIf="artistWorkshops.length > 0"
  primaryColor="#E76F51"
  secondaryColor="#C1440E"
  backgroundGradient="from-brick-50 via-background-light to-primary-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Upcoming Workshops"
    subtitle="Learn Mithila art techniques directly from {{artist?.name}}"
  ></app-section-title>

  <!-- Workshops Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
    <div *ngFor="let workshop of artistWorkshops" class="bg-white/80 backdrop-blur-sm rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
      <div class="relative overflow-hidden h-48">
        <!-- Workshop Image -->
        <img [src]="workshop.image" [alt]="workshop.title" class="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110">

        <!-- Overlay Gradient -->
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>

        <!-- Date Badge -->
        <div class="absolute top-4 right-4 bg-white text-primary-600 px-3 py-2 rounded-lg font-bold text-sm">
          {{workshop.date | date:'MMM d, yyyy'}}
        </div>
      </div>

      <div class="p-6">
        <h3 class="text-xl font-bold mb-2 text-gray-900">{{workshop.title}}</h3>
        <p class="text-gray-600 mb-4">{{workshop.description | slice:0:100}}...</p>

        <div class="flex flex-wrap gap-2 mb-4">
          <span class="inline-block px-3 py-1 bg-secondary-100 text-secondary-800 rounded-full text-sm">
            {{workshop.duration}}
          </span>
          <span class="inline-block px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-sm">
            {{workshop.level}}
          </span>
          <span class="inline-block px-3 py-1 bg-accent-100 text-accent-800 rounded-full text-sm">
            ${{workshop.price}}
          </span>
        </div>

        <a href="#" class="inline-block w-full text-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-300">
          Register Now
        </a>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Related Artists Section -->
<app-mithila-section *ngIf="relatedArtists.length > 0"
  primaryColor="#3B945E"
  secondaryColor="#F4B400"
  backgroundGradient="from-primary-50 via-background-light to-secondary-50"
  backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Similar Artists"
    subtitle="Explore more artists with similar styles"
  ></app-section-title>

  <!-- Related Artists Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
    <div *ngFor="let relatedArtist of relatedArtists" class="bg-white/80 backdrop-blur-sm rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
      <div class="relative overflow-hidden h-64">
        <!-- Artist Image -->
        <img [src]="relatedArtist.image" [alt]="relatedArtist.name" class="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110">

        <!-- Overlay Gradient -->
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>

      <div class="p-6">
        <h3 class="text-xl font-bold mb-2 text-gray-900">{{relatedArtist.name}}</h3>
        <p class="text-gray-600 mb-4">{{relatedArtist.role}}</p>
        <p class="text-gray-700 mb-4">{{relatedArtist.bio | slice:0:100}}...</p>

        <a [routerLink]="['/artists', relatedArtist.id]" class="inline-block w-full text-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-300">
          View Profile
        </a>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Connect with Artist Section -->
<app-mithila-section *ngIf="artist"
  primaryColor="#264653"
  secondaryColor="#F4B400"
  backgroundGradient="from-secondary-50 via-background-light to-accent-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <div class="max-w-4xl mx-auto">
    <div class="bg-gradient-to-r from-accent-700 to-primary-700 rounded-lg p-8 shadow-lg text-white relative overflow-hidden">
      <!-- Dark Overlay for Better Text Visibility -->
      <div class="absolute inset-0 bg-black/30"></div>

      <!-- Decorative Background Pattern -->
      <div class="absolute inset-0 opacity-5">
        <app-mithila-art-background
          [primaryColor]="'#FFFFFF'"
          [secondaryColor]="'#FFFFFF'"
          opacity="5">
        </app-mithila-art-background>
      </div>

      <div class="relative z-10 text-center">
        <app-mithila-decorative-element
          [primaryColor]="'#3B945E'"
          [secondaryColor]="'#F4B400'"
          [type]="'lotus'"
          position="relative mx-auto mb-6"
          classes="opacity-90"
          size="60px">
        </app-mithila-decorative-element>

        <h2 class="text-3xl font-bold mb-4 text-white drop-shadow-md">Connect with {{artist.name}}</h2>
        <p class="mb-8 text-white font-medium text-lg drop-shadow-md max-w-2xl mx-auto">
          Follow {{artist.name}} on social media to stay updated with their latest artworks, exhibitions, and workshops.
        </p>

        <!-- Social Media Links -->
        <div class="flex justify-center space-x-6">
          <a *ngIf="artist.socialMedia?.instagram" href="https://instagram.com/{{artist.socialMedia.instagram}}" target="_blank" class="group">
            <div class="bg-white/20 hover:bg-white/30 p-4 rounded-full transition-all duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
              </svg>
            </div>
            <span class="block mt-2 text-sm text-white/90">Instagram</span>
          </a>

          <a *ngIf="artist.socialMedia?.facebook" href="https://facebook.com/{{artist.socialMedia.facebook}}" target="_blank" class="group">
            <div class="bg-white/20 hover:bg-white/30 p-4 rounded-full transition-all duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
              </svg>
            </div>
            <span class="block mt-2 text-sm text-white/90">Facebook</span>
          </a>

          <a *ngIf="artist.socialMedia?.twitter" href="https://twitter.com/{{artist.socialMedia.twitter}}" target="_blank" class="group">
            <div class="bg-white/20 hover:bg-white/30 p-4 rounded-full transition-all duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
              </svg>
            </div>
            <span class="block mt-2 text-sm text-white/90">Twitter</span>
          </a>

          <a *ngIf="artist.socialMedia?.website" href="https://{{artist.socialMedia.website}}" target="_blank" class="group">
            <div class="bg-white/20 hover:bg-white/30 p-4 rounded-full transition-all duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
              </svg>
            </div>
            <span class="block mt-2 text-sm text-white/90">Website</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Back to Artists Button -->
<div class="container mx-auto px-4 py-12 text-center">
  <a routerLink="/artists" class="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-300">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
      <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
    </svg>
    Back to All Artists
  </a>
</div>
