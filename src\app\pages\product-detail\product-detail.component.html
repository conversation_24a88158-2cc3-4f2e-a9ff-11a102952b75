<div *ngIf="product" class="min-h-screen bg-gray-50">
  <!-- Breadcrumb -->
  <div class="bg-white border-b">
    <div class="container mx-auto px-4 py-4">
      <nav class="flex items-center space-x-2 text-sm">
        <a routerLink="/" class="text-primary-600 hover:text-primary-700">Home</a>
        <span class="text-gray-400">/</span>
        <a routerLink="/products" class="text-primary-600 hover:text-primary-700">Products</a>
        <span class="text-gray-400">/</span>
        <span class="text-gray-600">{{product.name}}</span>
      </nav>
    </div>
  </div>

  <!-- Product Details -->
  <div class="container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
      <!-- Product Images -->
      <div class="space-y-4">
        <!-- Main Image -->
        <div class="relative aspect-square overflow-hidden rounded-xl shadow-lg">
          <img 
            [src]="product.images[selectedImageIndex]" 
            [alt]="product.name"
            class="w-full h-full object-cover">
          
          <!-- Image Navigation -->
          <div *ngIf="product.images.length > 1" class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
            <div class="flex space-x-2">
              <button 
                *ngFor="let image of product.images; let i = index"
                (click)="selectImage(i)"
                [class]="selectedImageIndex === i ? 'bg-white' : 'bg-white/50'"
                class="w-3 h-3 rounded-full transition-all duration-300">
              </button>
            </div>
          </div>
        </div>

        <!-- Thumbnail Images -->
        <div *ngIf="product.images.length > 1" class="grid grid-cols-4 gap-2">
          <button 
            *ngFor="let image of product.images; let i = index"
            (click)="selectImage(i)"
            [class]="selectedImageIndex === i ? 'ring-2 ring-primary-500' : 'ring-1 ring-gray-200'"
            class="aspect-square overflow-hidden rounded-lg transition-all duration-300">
            <img [src]="image" [alt]="product.name" class="w-full h-full object-cover">
          </button>
        </div>
      </div>

      <!-- Product Information -->
      <div class="space-y-6">
        <!-- Product Header -->
        <div>
          <div class="flex items-center gap-3 mb-2">
            <span class="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium">
              {{product.category}}
            </span>
            <span *ngIf="product.featured" 
                  class="px-3 py-1 bg-secondary-100 text-secondary-700 rounded-full text-sm font-medium">
              Featured
            </span>
          </div>
          
          <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{{product.name}}</h1>
          
          <div class="flex items-center justify-between mb-6">
            <span class="text-3xl font-bold text-primary-600">{{formatPrice(product.price)}}</span>
            <span [class]="product.inStock ? 'text-green-600' : 'text-red-600'" 
                  class="font-medium">
              {{product.inStock ? '✓ In Stock' : '✗ Out of Stock'}}
            </span>
          </div>
        </div>

        <!-- Product Description -->
        <div class="prose prose-gray max-w-none">
          <p class="text-lg text-gray-700 leading-relaxed">{{product.description}}</p>
          <p *ngIf="product.detailedDescription" class="text-gray-600 mt-4">
            {{product.detailedDescription}}
          </p>
        </div>

        <!-- Product Details -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Artist Information -->
          <div class="bg-white p-6 rounded-xl shadow-sm">
            <h3 class="font-bold text-gray-900 mb-3 flex items-center">
              <svg class="h-5 w-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
              Artist
            </h3>
            <p class="font-medium text-gray-900 mb-2">{{product.artist}}</p>
            <p *ngIf="product.artistBio" class="text-sm text-gray-600">{{product.artistBio}}</p>
          </div>

          <!-- Specifications -->
          <div class="bg-white p-6 rounded-xl shadow-sm">
            <h3 class="font-bold text-gray-900 mb-3 flex items-center">
              <svg class="h-5 w-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"/>
              </svg>
              Specifications
            </h3>
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="text-gray-600">Dimensions:</span>
                <span class="font-medium">{{product.dimensions}}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Materials:</span>
                <span class="font-medium">{{product.materials.join(', ')}}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Cultural Significance -->
        <div *ngIf="product.culturalSignificance" class="bg-amber-50 p-6 rounded-xl border border-amber-200">
          <h3 class="font-bold text-amber-900 mb-3 flex items-center">
            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
            </svg>
            Cultural Significance
          </h3>
          <p class="text-amber-800">{{product.culturalSignificance}}</p>
        </div>

        <!-- Order Button -->
        <div class="flex gap-4">
          <button 
            (click)="toggleOrderForm()"
            [disabled]="!product.inStock"
            [class]="product.inStock ? 
              'bg-primary-600 hover:bg-primary-700 text-white' : 
              'bg-gray-300 text-gray-500 cursor-not-allowed'"
            class="flex-1 px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105">
            {{product.inStock ? 'Order This Piece' : 'Currently Unavailable'}}
          </button>
          
          <a routerLink="/products" 
             class="px-6 py-4 border-2 border-primary-600 text-primary-600 rounded-xl font-medium hover:bg-primary-50 transition-colors duration-300">
            Back to Products
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Order Form -->
  <div *ngIf="showOrderForm" id="order-form" class="bg-white py-16">
    <div class="container mx-auto px-4 max-w-2xl">
      <div class="text-center mb-8">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">Order Form</h2>
        <p class="text-gray-600">Please fill out the form below to place your order. We'll contact you to confirm details and arrange payment.</p>
      </div>

      <form [formGroup]="orderForm" (ngSubmit)="onSubmitOrder()" class="space-y-6">
        <!-- Customer Information -->
        <div class="bg-gray-50 p-6 rounded-xl">
          <h3 class="text-xl font-bold text-gray-900 mb-4">Customer Information</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
              <input 
                type="text" 
                formControlName="customerName"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
              <div *ngIf="getFieldError('customerName')" class="text-red-500 text-sm mt-1">
                {{getFieldError('customerName')}}
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
              <input 
                type="email" 
                formControlName="email"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
              <div *ngIf="getFieldError('email')" class="text-red-500 text-sm mt-1">
                {{getFieldError('email')}}
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
              <input 
                type="tel" 
                formControlName="phone"
                placeholder="9876543210"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
              <div *ngIf="getFieldError('phone')" class="text-red-500 text-sm mt-1">
                {{getFieldError('phone')}}
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Quantity *</label>
              <input 
                type="number" 
                formControlName="quantity"
                min="1"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
              <div *ngIf="getFieldError('quantity')" class="text-red-500 text-sm mt-1">
                {{getFieldError('quantity')}}
              </div>
            </div>
          </div>
        </div>

        <!-- Shipping Address -->
        <div class="bg-gray-50 p-6 rounded-xl">
          <h3 class="text-xl font-bold text-gray-900 mb-4">Shipping Address</h3>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Address *</label>
              <textarea 
                formControlName="address"
                rows="3"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"></textarea>
              <div *ngIf="getFieldError('address')" class="text-red-500 text-sm mt-1">
                {{getFieldError('address')}}
              </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">City *</label>
                <input 
                  type="text" 
                  formControlName="city"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <div *ngIf="getFieldError('city')" class="text-red-500 text-sm mt-1">
                  {{getFieldError('city')}}
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Postal Code *</label>
                <input 
                  type="text" 
                  formControlName="postalCode"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <div *ngIf="getFieldError('postalCode')" class="text-red-500 text-sm mt-1">
                  {{getFieldError('postalCode')}}
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Country *</label>
                <input 
                  type="text" 
                  formControlName="country"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <div *ngIf="getFieldError('country')" class="text-red-500 text-sm mt-1">
                  {{getFieldError('country')}}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Special Requests -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Special Requests (Optional)</label>
          <textarea 
            formControlName="specialRequests"
            rows="3"
            placeholder="Any special requirements or customizations..."
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"></textarea>
        </div>

        <!-- Order Summary -->
        <div class="bg-primary-50 p-6 rounded-xl">
          <h3 class="text-xl font-bold text-gray-900 mb-4">Order Summary</h3>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span>Product:</span>
              <span class="font-medium">{{product.name}}</span>
            </div>
            <div class="flex justify-between">
              <span>Price per item:</span>
              <span class="font-medium">{{formatPrice(product.price)}}</span>
            </div>
            <div class="flex justify-between">
              <span>Quantity:</span>
              <span class="font-medium">{{orderForm.get('quantity')?.value || 1}}</span>
            </div>
            <hr class="my-2">
            <div class="flex justify-between text-lg font-bold">
              <span>Total:</span>
              <span class="text-primary-600">{{formatPrice(getTotalPrice())}}</span>
            </div>
          </div>
        </div>

        <!-- Terms and Submit -->
        <div class="space-y-4">
          <div class="flex items-start">
            <input 
              type="checkbox" 
              formControlName="agreeToTerms"
              class="mt-1 mr-3 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
            <label class="text-sm text-gray-700">
              I agree to the terms and conditions and understand that this is an order request. 
              Payment details will be discussed upon confirmation. *
            </label>
          </div>
          <div *ngIf="getFieldError('agreeToTerms')" class="text-red-500 text-sm">
            You must agree to the terms and conditions
          </div>
          
          <div class="flex gap-4">
            <button 
              type="submit"
              [disabled]="!orderForm.valid"
              class="flex-1 bg-primary-600 text-white px-8 py-4 rounded-xl font-bold hover:bg-primary-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-300">
              Submit Order Request
            </button>
            
            <button 
              type="button"
              (click)="toggleOrderForm()"
              class="px-6 py-4 border-2 border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors duration-300">
              Cancel
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Order Success Message -->
  <div *ngIf="orderSubmitted" id="order-success" class="bg-green-50 py-16">
    <div class="container mx-auto px-4 text-center max-w-2xl">
      <div class="bg-white p-8 rounded-xl shadow-lg">
        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Order Request Submitted!</h2>
        <p class="text-gray-600 mb-6">
          Thank you for your interest in "{{product.name}}". We have received your order request and will contact you within 24 hours to confirm the details and discuss payment options.
        </p>
        <div class="flex gap-4 justify-center">
          <a routerLink="/products" 
             class="bg-primary-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors duration-300">
            Continue Shopping
          </a>
          <a routerLink="/contact" 
             class="border-2 border-primary-600 text-primary-600 px-6 py-3 rounded-lg font-medium hover:bg-primary-50 transition-colors duration-300">
            Contact Us
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
