<div *ngIf="product" class="min-h-screen bg-white">
  <!-- Simple Breadcrumb -->
  <div class="bg-gray-50 border-b">
    <div class="container mx-auto px-4 py-3">
      <nav class="flex items-center space-x-2 text-sm text-gray-600">
        <a routerLink="/" class="hover:text-primary-600">Home</a>
        <span>/</span>
        <a routerLink="/products" class="hover:text-primary-600">Products</a>
        <span>/</span>
        <span class="text-gray-900">{{product.name}}</span>
      </nav>
    </div>
  </div>

  <!-- Product Details -->
  <div class="container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Product Images -->
      <div class="space-y-4">
        <!-- Main Image -->
        <div class="aspect-square overflow-hidden rounded-lg bg-gray-100">
          <img
            [src]="product.images[selectedImageIndex]"
            [alt]="product.name"
            class="w-full h-full object-cover">
        </div>

        <!-- Thumbnail Images -->
        <div *ngIf="product.images.length > 1" class="grid grid-cols-4 gap-2">
          <button
            *ngFor="let image of product.images; let i = index"
            (click)="selectImage(i)"
            [class]="selectedImageIndex === i ? 'ring-2 ring-primary-600' : 'ring-1 ring-gray-200'"
            class="aspect-square overflow-hidden rounded transition-all">
            <img [src]="image" [alt]="product.name" class="w-full h-full object-cover">
          </button>
        </div>
      </div>

      <!-- Product Information -->
      <div class="space-y-6">
        <!-- Product Header -->
        <div>
          <div class="flex items-center gap-2 mb-3">
            <span class="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium">
              {{product.category}}
            </span>
            <span *ngIf="product.featured"
                  class="px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-sm font-medium">
              Featured
            </span>
          </div>

          <h1 class="text-3xl font-bold text-gray-900 mb-4">{{product.name}}</h1>

          <div class="flex items-center justify-between mb-6">
            <span class="text-2xl font-bold text-primary-600">{{formatPrice(product.price)}}</span>
            <div class="text-right">
              <div class="text-sm text-gray-500">Artist</div>
              <div class="font-medium text-gray-900">{{product.artist}}</div>
            </div>
          </div>
        </div>

        <!-- Description -->
        <div>
          <h2 class="text-lg font-semibold text-gray-900 mb-3">Description</h2>
          <p class="text-gray-600 leading-relaxed mb-4">{{product.description}}</p>
          <p *ngIf="product.detailedDescription" class="text-gray-600 leading-relaxed">
            {{product.detailedDescription}}
          </p>
        </div>

        <!-- Add to Cart -->
        <div class="border-t pt-6">
          <!-- Quantity Selector -->
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <span class="text-sm font-medium text-gray-700">Quantity:</span>
              <div class="flex items-center border rounded">
                <button
                  (click)="decreaseQuantity()"
                  class="px-3 py-1 text-gray-600 hover:bg-gray-100">
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                  </svg>
                </button>
                <span class="px-4 py-1 font-medium">{{quantity}}</span>
                <button
                  (click)="increaseQuantity()"
                  class="px-3 py-1 text-gray-600 hover:bg-gray-100">
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                  </svg>
                </button>
              </div>
            </div>

            <div class="text-right">
              <div class="text-sm text-gray-500">Total</div>
              <div class="text-lg font-bold text-primary-600">{{formatPrice(getTotalPrice())}}</div>
            </div>
          </div>

          <!-- Add to Cart Button -->
          <button
            (click)="addToCart()"
            class="w-full bg-primary-600 text-white py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors mb-3">
            Add to Cart
          </button>

          <!-- Cart Status -->
          <div *ngIf="isInCart()" class="text-center">
            <span class="text-sm text-green-600">✓ {{getCartQuantity()}} in cart</span>
          </div>
        </div>

        <!-- Product Details -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 border-t pt-6">
          <!-- Artist & Specs -->
          <div>
            <h3 class="font-semibold text-gray-900 mb-3">Artist</h3>
            <p class="text-gray-600 mb-4">{{product.artist}}</p>
            <p *ngIf="product.artistBio" class="text-sm text-gray-600">{{product.artistBio}}</p>
          </div>

          <div>
            <h3 class="font-semibold text-gray-900 mb-3">Specifications</h3>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600">Size:</span>
                <span class="text-gray-900">{{product.dimensions}}</span>
              </div>
              <div>
                <span class="text-gray-600">Materials:</span>
                <div class="mt-1">
                  <span *ngFor="let material of product.materials; let last = last"
                        class="text-gray-900">
                    {{material}}<span *ngIf="!last">, </span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Cultural Significance -->
        <div *ngIf="product.culturalSignificance" class="bg-amber-50 p-4 rounded-lg border border-amber-200">
          <h3 class="font-semibold text-amber-900 mb-2">Cultural Significance</h3>
          <p class="text-amber-800 text-sm">{{product.culturalSignificance}}</p>
        </div>

        <!-- Back Button -->
        <div class="text-center pt-6">
          <a routerLink="/products"
             class="inline-flex items-center space-x-2 text-primary-600 hover:text-primary-700">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            <span>Back to Products</span>
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Simple Cart Modal -->
  <div *ngIf="showCartModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-sm mx-4">
      <div class="text-center">
        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
          <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
          </svg>
        </div>
        <h3 class="font-semibold text-gray-900 mb-2">Added to Cart</h3>
        <p class="text-gray-600 text-sm mb-4">{{quantity}} item(s) added successfully</p>

        <div class="flex gap-2">
          <button
            (click)="showCartModal = false"
            class="flex-1 px-3 py-2 border text-gray-700 rounded hover:bg-gray-50 text-sm">
            Continue
          </button>
          <button
            (click)="viewCart()"
            class="flex-1 px-3 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 text-sm">
            View Cart
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
