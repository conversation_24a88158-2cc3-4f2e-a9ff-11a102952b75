import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MithilaArtBackgroundComponent } from '../mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../mithila-border/mithila-border.component';
import { MithilaDecorativeElementComponent } from '../mithila-decorative-element/mithila-decorative-element.component';

@Component({
  selector: 'app-mithila-section',
  standalone: true,
  imports: [
    CommonModule,
    MithilaArtBackgroundComponent,
    MithilaBorderComponent,
    MithilaDecorativeElementComponent
  ],
  template: `
    <section class="relative overflow-hidden {{padding}} {{classes}}">
      <!-- Background Gradient -->
      <div class="absolute inset-0 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-bl {{backgroundGradient}} animate-gradient-background opacity-70"></div>
      </div>
      
      <!-- Mithila Art Background Pattern -->
      <app-mithila-art-background 
        [primaryColor]="primaryColor" 
        [secondaryColor]="secondaryColor"
        [opacity]="backgroundOpacity">
      </app-mithila-art-background>
      
      <!-- Decorative Border -->
      <app-mithila-border
        [primaryColor]="primaryColor"
        [secondaryColor]="secondaryColor"
        [type]="'full'"
        position="top-8 left-8 right-8 bottom-8">
      </app-mithila-border>
      
      <!-- Decorative Elements -->
      <app-mithila-decorative-element
        *ngIf="showDecorativeElements"
        [primaryColor]="primaryColor"
        [secondaryColor]="secondaryColor"
        [type]="'lotus'"
        position="absolute top-20 right-20"
        classes="opacity-30 animate-float-slow pointer-events-none"
        size="80px">
      </app-mithila-decorative-element>
      
      <app-mithila-decorative-element
        *ngIf="showDecorativeElements"
        [primaryColor]="secondaryColor"
        [secondaryColor]="primaryColor"
        [type]="'peacock'"
        position="absolute bottom-20 left-20"
        classes="opacity-30 animate-float-medium pointer-events-none"
        size="60px">
      </app-mithila-decorative-element>
      
      <app-mithila-decorative-element
        *ngIf="showDecorativeElements"
        [primaryColor]="primaryColor"
        [secondaryColor]="secondaryColor"
        [type]="'geometric'"
        position="absolute top-1/3 left-1/4"
        classes="opacity-20 animate-pulse-slow pointer-events-none"
        size="40px">
      </app-mithila-decorative-element>
      
      <!-- Content Container -->
      <div class="container relative z-10">
        <ng-content></ng-content>
      </div>
    </section>
  `,
  styles: []
})
export class MithilaSectionComponent {
  @Input() primaryColor: string = '#C1440E'; // Terracotta Red
  @Input() secondaryColor: string = '#F4B400'; // Sunflower Yellow
  @Input() backgroundGradient: string = 'from-primary-50 via-background-light to-secondary-50';
  @Input() backgroundOpacity: string = '10';
  @Input() padding: string = 'py-16 sm:py-20 md:py-24';
  @Input() classes: string = '';
  @Input() showDecorativeElements: boolean = true;
}
