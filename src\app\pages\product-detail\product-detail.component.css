/* Image gallery styles */
.image-gallery {
  position: relative;
}

.main-image {
  transition: transform 0.3s ease;
}

.main-image:hover {
  transform: scale(1.05);
}

.thumbnail-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
}

.thumbnail {
  aspect-ratio: 1;
  overflow: hidden;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.thumbnail:hover {
  transform: scale(1.05);
}

.thumbnail.active {
  ring: 2px solid #c1440e;
}

/* Product information styles */
.product-header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
  margin-bottom: 1.5rem;
}

.product-badges {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.badge-category {
  background-color: #fef3c7;
  color: #92400e;
}

.badge-featured {
  background-color: #ddd6fe;
  color: #7c3aed;
}

/* Form styles */
.form-section {
  background-color: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.75rem;
  margin-bottom: 1.5rem;
}

.form-section h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
}

.form-grid {
  display: grid;
  gap: 1rem;
}

.form-grid-2 {
  grid-template-columns: repeat(1, 1fr);
}

@media (min-width: 768px) {
  .form-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

.form-grid-3 {
  grid-template-columns: repeat(1, 1fr);
}

@media (min-width: 768px) {
  .form-grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  ring: 2px solid #c1440e;
  border-color: transparent;
}

.form-textarea {
  resize: vertical;
  min-height: 6rem;
}

.form-error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Order summary styles */
.order-summary {
  background-color: #fef3c7;
  padding: 1.5rem;
  border-radius: 0.75rem;
  border: 1px solid #fbbf24;
}

.order-summary h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.summary-total {
  font-size: 1.125rem;
  font-weight: 700;
  padding-top: 0.5rem;
  border-top: 1px solid #d1d5db;
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.btn-primary {
  background-color: #c1440e;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #a03a0c;
  transform: translateY(-1px);
}

.btn-primary:disabled {
  background-color: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: transparent;
  color: #c1440e;
  border: 2px solid #c1440e;
}

.btn-secondary:hover {
  background-color: #fef3c7;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* Success message styles */
.success-container {
  background-color: #f0fdf4;
  padding: 4rem 1rem;
}

.success-card {
  background-color: white;
  padding: 2rem;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 32rem;
  margin: 0 auto;
}

.success-icon {
  width: 4rem;
  height: 4rem;
  background-color: #dcfce7;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
}

.success-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
}

.success-message {
  color: #6b7280;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .product-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .thumbnail-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .btn-group {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .btn-group .btn {
    width: 100%;
  }
}

/* Animation classes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

/* Checkbox styles */
.custom-checkbox {
  appearance: none;
  width: 1rem;
  height: 1rem;
  border: 2px solid #d1d5db;
  border-radius: 0.25rem;
  background-color: white;
  cursor: pointer;
  position: relative;
}

.custom-checkbox:checked {
  background-color: #c1440e;
  border-color: #c1440e;
}

.custom-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #c1440e;
  border-radius: 50%;
  width: 1rem;
  height: 1rem;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
