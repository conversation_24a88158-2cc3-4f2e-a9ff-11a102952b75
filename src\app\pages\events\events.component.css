/* Events page specific styles */

/* Animation for floating elements */
@keyframes float-slow {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.animate-float-slow {
  animation: float-slow 6s ease-in-out infinite;
}

/* Animation for fading in elements */
@keyframes fade-in {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fade-in 1s ease-out forwards;
}

/* Animation for delayed fade in */
@keyframes fade-in-delay {
  0%, 50% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

.animate-fade-in-delay {
  animation: fade-in-delay 2s ease-out forwards;
}

/* Animation for border gradient */
@keyframes border-gradient {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 0.3; }
}

.animate-border-gradient {
  animation: border-gradient 4s ease-in-out infinite;
}

/* Animation for gradient background */
@keyframes gradient-background {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient-background {
  animation: gradient-background 15s ease infinite;
  background-size: 200% 200%;
}

/* Animation for shimmer effect */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.animate-shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(to right, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

/* Calendar styles */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
}

.calendar-day {
  aspect-ratio: 1/1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 0.5rem;
  position: relative;
  transition: all 0.3s ease;
}

.calendar-day:hover {
  transform: scale(1.05);
}

.calendar-day.has-event::after {
  content: '';
  position: absolute;
  bottom: 0.5rem;
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: currentColor;
}

/* Category card hover effects */
.category-card {
  transition: all 0.3s ease;
  overflow: hidden;
}

.category-card:hover {
  transform: translateY(-5px);
}

.category-card:hover .category-image {
  transform: scale(1.1);
}

.category-image {
  transition: transform 0.5s ease;
}

/* Event card styles */
.event-card {
  transition: all 0.3s ease;
  overflow: hidden;
}

.event-card:hover {
  transform: translateY(-5px);
}

.event-card:hover .event-image {
  transform: scale(1.1);
}

.event-image {
  transition: transform 0.5s ease;
}

/* Badge styles */
.event-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-weight: 600;
  font-size: 0.75rem;
  z-index: 10;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .calendar-grid {
    grid-template-columns: repeat(7, 1fr);
    gap: 0.25rem;
  }

  .calendar-day {
    font-size: 0.75rem;
  }
}
