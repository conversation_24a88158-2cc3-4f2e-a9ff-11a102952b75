/* Artist detail page specific styles */

/* Animation for floating elements */
@keyframes float-slow {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.animate-float-slow {
  animation: float-slow 6s ease-in-out infinite;
}

/* Animation for fading in elements */
@keyframes fade-in {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fade-in 1s ease-out forwards;
}

/* Animation for border gradient */
@keyframes border-gradient {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 0.3; }
}

.animate-border-gradient {
  animation: border-gradient 4s ease-in-out infinite;
}

/* Animation for gradient background */
@keyframes gradient-background {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient-background {
  animation: gradient-background 15s ease infinite;
  background-size: 200% 200%;
}

/* Animation for gradient shift */
@keyframes gradient-shift {
  0%, 100% { transform: translateX(0) translateY(0); }
  50% { transform: translateX(5%) translateY(-5%); }
}

.animate-gradient-shift {
  animation: gradient-shift 10s ease-in-out infinite;
}

/* Animation for pulse effect */
@keyframes pulse-slow {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.3; }
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

/* Custom styles for artist detail page */
.artist-quote {
  position: relative;
  font-style: italic;
  padding: 1.5rem;
  margin: 2rem 0;
  background-color: rgba(255, 255, 255, 0.1);
  border-left: 4px solid var(--color-primary-500);
  border-radius: 0.5rem;
}

.artist-quote::before,
.artist-quote::after {
  content: '"';
  font-size: 2rem;
  color: var(--color-primary-500);
  position: absolute;
  opacity: 0.5;
}

.artist-quote::before {
  top: 0;
  left: 0.5rem;
}

.artist-quote::after {
  bottom: -0.5rem;
  right: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .artist-grid {
    grid-template-columns: 1fr;
  }
}
