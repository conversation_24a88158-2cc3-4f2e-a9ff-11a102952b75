import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeroBannerComponent } from '../../components/shared/hero-banner/hero-banner.component';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../../components/shared/mithila-border/mithila-border.component';
import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-gallery',
  standalone: true,
  imports: [
    CommonModule,
    SectionTitleComponent,
    MithilaSectionComponent,
    MithilaArtBackgroundComponent,
    MithilaBorderComponent,
    MithilaDecorativeElementComponent
  ],
  templateUrl: './gallery.component.html',
  styleUrl: './gallery.component.css'
})
export class GalleryComponent {
  // Gallery categories
  categories = [
    {
      id: 'traditional',
      name: 'Traditional',
      description: 'Classic Mithila art featuring traditional motifs, symbols, and stories from ancient folklore.',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      color: '#C1440E'
    },
    {
      id: 'contemporary',
      name: 'Contemporary',
      description: 'Modern interpretations of Mithila art that blend traditional techniques with contemporary themes.',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      color: '#F4B400'
    },
    {
      id: 'religious',
      name: 'Religious',
      description: 'Artwork depicting Hindu deities, religious ceremonies, and spiritual symbolism.',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      color: '#264653'
    },
    {
      id: 'nature',
      name: 'Nature & Wildlife',
      description: 'Mithila art celebrating the natural world, featuring flora, fauna, and environmental themes.',
      image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
      color: '#3B945E'
    }
  ];

  // Featured artworks
  featuredArtworks = [
    {
      id: 1,
      title: 'Celebration of Life',
      artist: 'Sarita Devi',
      year: 2022,
      category: 'Traditional',
      description: 'A vibrant depiction of village life during festival season, showcasing the rich cultural traditions of Mithila.',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      dimensions: '24 × 36 inches',
      medium: 'Natural pigments on handmade paper'
    },
    {
      id: 2,
      title: 'Sacred Geometry',
      artist: 'Ramesh Kumar',
      year: 2021,
      category: 'Contemporary',
      description: 'A modern interpretation of traditional Mithila patterns, exploring the mathematical precision in ancient art forms.',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      dimensions: '18 × 24 inches',
      medium: 'Acrylic on canvas'
    },
    {
      id: 3,
      title: 'Divine Feminine',
      artist: 'Anita Jha',
      year: 2023,
      category: 'Religious',
      description: 'A powerful representation of the goddess Durga, embodying strength, protection, and maternal energy.',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      dimensions: '30 × 40 inches',
      medium: 'Natural pigments and gold leaf on handmade paper'
    },
    {
      id: 4,
      title: 'Harmony of Nature',
      artist: 'Sunil Yadav',
      year: 2022,
      category: 'Nature & Wildlife',
      description: 'A detailed portrayal of the delicate balance between humans, animals, and plants in the Mithila ecosystem.',
      image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
      dimensions: '24 × 36 inches',
      medium: 'Natural dyes on handmade paper'
    },
    {
      id: 5,
      title: 'Wedding Ceremony',
      artist: 'Sarita Devi',
      year: 2021,
      category: 'Traditional',
      description: 'A detailed illustration of a traditional Mithila wedding, showcasing the rituals, customs, and celebrations.',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      dimensions: '36 × 48 inches',
      medium: 'Natural pigments on handmade paper'
    },
    {
      id: 6,
      title: 'Urban Mithila',
      artist: 'Ramesh Kumar',
      year: 2023,
      category: 'Contemporary',
      description: 'A fusion of traditional Mithila art with urban landscapes, exploring the intersection of ancient and modern worlds.',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      dimensions: '24 × 36 inches',
      medium: 'Mixed media on canvas'
    }
  ];

  // Exhibition information
  upcomingExhibitions = [
    {
      title: 'Traditions Reimagined',
      dates: 'June 15 - July 30, 2024',
      location: 'Main Gallery, Mithilani Ghar',
      description: 'Exploring how contemporary artists are reinterpreting traditional Mithila art forms for modern audiences.',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg'
    },
    {
      title: 'Sacred Visions',
      dates: 'August 10 - September 25, 2024',
      location: 'Special Exhibition Hall, Mithilani Ghar',
      description: 'A collection of religious and spiritual artwork showcasing the divine in Mithila artistic tradition.',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg'
    },
    {
      title: 'Nature\'s Palette',
      dates: 'October 5 - November 20, 2024',
      location: 'Garden Gallery, Mithilani Ghar',
      description: 'Celebrating the natural world through the distinctive style and techniques of Mithila art.',
      image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg'
    }
  ];

  // Filter functionality
  activeCategory: string = 'all';

  setActiveCategory(category: string) {
    this.activeCategory = category;
  }

  get filteredArtworks() {
    if (this.activeCategory === 'all') {
      return this.featuredArtworks;
    }
    return this.featuredArtworks.filter(artwork =>
      artwork.category.toLowerCase() === this.activeCategory.toLowerCase()
    );
  }
}
