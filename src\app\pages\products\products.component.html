<!-- Simple Header -->
<div class="bg-white py-12">
  <div class="container mx-auto px-4 text-center">
    <h1 class="text-4xl font-bold text-gray-900 mb-4">Art Collection</h1>
    <p class="text-gray-600 max-w-2xl mx-auto">Authentic Mithila art pieces crafted by local artists</p>
  </div>
</div>

<!-- Products Section -->
<section class="py-8 bg-gray-50">
  <div class="container mx-auto px-4">
    <!-- Category Filter -->
    <div class="flex flex-wrap justify-center gap-3 mb-8">
      <button
        *ngFor="let category of categories"
        (click)="filterByCategory(category)"
        [class]="selectedCategory === category ?
          'bg-primary-600 text-white' :
          'bg-white text-gray-600 hover:bg-gray-100'"
        class="px-4 py-2 rounded-lg border transition-colors font-medium text-sm">
        {{category}}
      </button>
    </div>

    <!-- Products Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        *ngFor="let product of filteredProducts"
        class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 overflow-hidden group">

        <!-- Product Image -->
        <div class="relative h-48 overflow-hidden">
          <img
            [src]="product.images[0]"
            [alt]="product.name"
            class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">

          <!-- Featured Badge -->
          <div *ngIf="product.featured"
               class="absolute top-3 left-3 bg-primary-600 text-white px-2 py-1 rounded text-xs font-medium">
            Featured
          </div>
        </div>

        <!-- Product Info -->
        <div class="p-4">
          <div class="mb-2">
            <span class="text-xs text-primary-600 font-medium">{{product.category}}</span>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">{{product.name}}</h3>
          <p class="text-sm text-gray-600 mb-3">By {{product.artist}}</p>

          <!-- Price and Actions -->
          <div class="flex items-center justify-between">
            <span class="text-lg font-bold text-primary-600">{{formatPrice(product.price)}}</span>
            <div class="flex gap-2">
              <a [routerLink]="['/products', product.slug]"
                 class="px-3 py-1 bg-gray-100 text-gray-700 rounded text-sm hover:bg-gray-200 transition-colors">
                View
              </a>
              <button
                (click)="addToCart(product, $event)"
                [class]="isInCart(product.id) ?
                  'bg-green-600 text-white' :
                  'bg-primary-600 text-white hover:bg-primary-700'"
                class="px-3 py-1 rounded text-sm transition-colors">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path *ngIf="!isInCart(product.id)" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"/>
                  <path *ngIf="isInCart(product.id)" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="filteredProducts.length === 0" class="text-center py-12">
      <p class="text-gray-500">No products found in this category.</p>
    </div>
  </div>
</section>
