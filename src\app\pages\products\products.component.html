<!-- Hero Section -->
<div class="relative h-[60vh] overflow-hidden">
  <!-- Background Image -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
       style="background-image: url('https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg');">
  </div>
  
  <!-- Gradient Overlay -->
  <div class="absolute inset-0 bg-gradient-to-br from-primary-600/90 via-primary-500/80 to-secondary-600/90"></div>
  
  <!-- <PERSON><PERSON><PERSON> Background -->
  <div class="absolute inset-0 opacity-20">
    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <pattern id="hero-pattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
          <circle cx="30" cy="30" r="12" fill="none" stroke="#FFD700" stroke-width="1"/>
          <circle cx="30" cy="30" r="6" fill="none" stroke="#FFD700" stroke-width="1"/>
          <path d="M10,30 H20 M40,30 H50 M30,10 V20 M30,40 V50" stroke="#FFD700" stroke-width="1"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#hero-pattern)" />
    </svg>
  </div>

  <!-- Hero Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-5xl md:text-6xl font-bold text-white mb-6 font-display">
        Our Art Collection
      </h1>
      <p class="text-xl md:text-2xl text-white/90 mb-8 max-w-2xl mx-auto">
        Discover authentic Mithila art pieces crafted by skilled local artists
      </p>
      <div class="inline-block px-6 py-3 bg-white/10 backdrop-blur-md rounded-full border border-white/30">
        <span class="text-white font-medium">🎨 Handcrafted • 🌟 Authentic • 💝 Cultural Heritage</span>
      </div>
    </div>
  </div>
</div>

<!-- Products Section -->
<section class="py-16 bg-gray-50">
  <div class="container mx-auto px-4">
    <!-- Section Header -->
    <div class="text-center mb-12">
      <h2 class="text-4xl font-bold text-gray-900 mb-4">Browse Our Products</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Each piece tells a story of tradition, craftsmanship, and cultural heritage
      </p>
    </div>

    <!-- Category Filter -->
    <div class="flex flex-wrap justify-center gap-4 mb-12">
      <button 
        *ngFor="let category of categories"
        (click)="filterByCategory(category)"
        [class]="selectedCategory === category ? 
          'bg-primary-600 text-white' : 
          'bg-white text-gray-700 hover:bg-primary-50'"
        class="px-6 py-3 rounded-full border border-primary-200 transition-all duration-300 font-medium">
        {{category}}
      </button>
    </div>

    <!-- Products Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <div 
        *ngFor="let product of filteredProducts" 
        class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
        
        <!-- Product Image -->
        <div class="relative h-64 overflow-hidden">
          <img 
            [src]="product.images[0]" 
            [alt]="product.name"
            class="w-full h-full object-cover transition-transform duration-500 hover:scale-110">
          
          <!-- Featured Badge -->
          <div *ngIf="product.featured" 
               class="absolute top-4 left-4 bg-secondary-500 text-white px-3 py-1 rounded-full text-sm font-medium">
            Featured
          </div>
          
          <!-- Stock Status -->
          <div class="absolute top-4 right-4">
            <span [class]="product.inStock ? 
              'bg-green-500 text-white' : 
              'bg-red-500 text-white'"
              class="px-3 py-1 rounded-full text-sm font-medium">
              {{product.inStock ? 'In Stock' : 'Out of Stock'}}
            </span>
          </div>
          
          <!-- Category Badge -->
          <div class="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full">
            <span class="text-primary-600 text-sm font-medium">{{product.category}}</span>
          </div>
        </div>

        <!-- Product Info -->
        <div class="p-6">
          <h3 class="text-xl font-bold text-gray-900 mb-2 line-clamp-2">{{product.name}}</h3>
          <p class="text-gray-600 mb-4 line-clamp-3">{{product.description}}</p>
          
          <!-- Artist & Details -->
          <div class="space-y-2 mb-4">
            <div class="flex items-center text-sm text-gray-500">
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
              Artist: {{product.artist}}
            </div>
            <div class="flex items-center text-sm text-gray-500">
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"/>
              </svg>
              Size: {{product.dimensions}}
            </div>
          </div>
          
          <!-- Price -->
          <div class="flex items-center justify-between mb-4">
            <span class="text-2xl font-bold text-primary-600">{{formatPrice(product.price)}}</span>
          </div>
          
          <!-- Action Button -->
          <div class="flex gap-3">
            <a [routerLink]="['/products', product.id]" 
               class="flex-1 bg-primary-600 text-white px-4 py-3 rounded-lg hover:bg-primary-700 transition-colors duration-300 text-center font-medium">
              View Details
            </a>
            <button 
              [disabled]="!product.inStock"
              [class]="product.inStock ? 
                'bg-secondary-600 hover:bg-secondary-700 text-white' : 
                'bg-gray-300 text-gray-500 cursor-not-allowed'"
              class="px-4 py-3 rounded-lg transition-colors duration-300 font-medium">
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="filteredProducts.length === 0" class="text-center py-16">
      <div class="max-w-md mx-auto">
        <svg class="h-24 w-24 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"/>
        </svg>
        <h3 class="text-xl font-medium text-gray-900 mb-2">No products found</h3>
        <p class="text-gray-500">Try selecting a different category or check back later for new arrivals.</p>
      </div>
    </div>
  </div>
</section>

<!-- Call to Action Section -->
<section class="py-16 bg-primary-600">
  <div class="container mx-auto px-4 text-center">
    <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
      Can't Find What You're Looking For?
    </h2>
    <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
      We create custom Mithila art pieces. Contact us to discuss your specific requirements.
    </p>
    <a routerLink="/contact" 
       class="inline-block bg-white text-primary-600 px-8 py-4 rounded-full font-bold hover:bg-gray-100 transition-colors duration-300">
      Contact Us for Custom Orders
    </a>
  </div>
</section>
