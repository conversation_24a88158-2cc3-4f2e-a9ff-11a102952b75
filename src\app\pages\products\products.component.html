<!-- Hero Section -->
<div class="relative h-[60vh] overflow-hidden">
  <!-- Background Image -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
       style="background-image: url('https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg');">
  </div>

  <!-- Gradient Overlay -->
  <div class="absolute inset-0 bg-gradient-to-br from-primary-600/90 via-primary-500/80 to-secondary-600/90"></div>

  <!-- <PERSON><PERSON><PERSON> Background -->
  <div class="absolute inset-0 opacity-20">
    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <pattern id="hero-pattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
          <circle cx="30" cy="30" r="12" fill="none" stroke="#FFD700" stroke-width="1"/>
          <circle cx="30" cy="30" r="6" fill="none" stroke="#FFD700" stroke-width="1"/>
          <path d="M10,30 H20 M40,30 H50 M30,10 V20 M30,40 V50" stroke="#FFD700" stroke-width="1"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#hero-pattern)" />
    </svg>
  </div>

  <!-- Hero Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-5xl md:text-6xl font-bold text-white mb-6 font-display">
        Our Art Collection
      </h1>
      <p class="text-xl md:text-2xl text-white/90 mb-8 max-w-2xl mx-auto">
        Discover authentic Mithila art pieces crafted by skilled local artists
      </p>
      <div class="inline-block px-6 py-3 bg-white/10 backdrop-blur-md rounded-full border border-white/30">
        <span class="text-white font-medium">🎨 Handcrafted • 🌟 Authentic • 💝 Cultural Heritage</span>
      </div>
    </div>
  </div>
</div>

<!-- Products Section -->
<section class="py-16 bg-gray-50">
  <div class="container mx-auto px-4">
    <!-- Section Header -->
    <div class="text-center mb-12">
      <h2 class="text-4xl font-bold text-gray-900 mb-4">Browse Our Products</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Each piece tells a story of tradition, craftsmanship, and cultural heritage
      </p>
    </div>

    <!-- Category Filter -->
    <div class="flex flex-wrap justify-center gap-4 mb-12">
      <button
        *ngFor="let category of categories"
        (click)="filterByCategory(category)"
        [class]="selectedCategory === category ?
          'bg-primary-600 text-white shadow-lg transform scale-105' :
          'bg-white text-gray-700 hover:bg-primary-50 hover:shadow-md hover:transform hover:scale-105'"
        class="px-6 py-3 rounded-full border border-primary-200 transition-all duration-300 font-medium">
        {{category}}
      </button>
    </div>

    <!-- Products Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <div
        *ngFor="let product of filteredProducts; let i = index"
        class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 group animate-fade-in-up"
        [style.animation-delay]="(i * 100) + 'ms'">

        <!-- Product Image -->
        <div class="relative h-64 overflow-hidden">
          <img
            [src]="product.images[0]"
            [alt]="product.name"
            class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">

          <!-- Image Overlay -->
          <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

          <!-- Featured Badge -->
          <div *ngIf="product.featured"
               class="absolute top-4 left-4 bg-gradient-to-r from-secondary-500 to-secondary-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg animate-pulse">
            ⭐ Featured
          </div>

          <!-- Category Badge -->
          <div class="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
            <span class="text-primary-600 text-sm font-medium">{{product.category}}</span>
          </div>

          <!-- Quick Actions Overlay -->
          <div class="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
            <a [routerLink]="['/products', product.slug]"
               class="w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-gray-700 hover:bg-white hover:text-primary-600 transition-all duration-300 transform hover:scale-110 shadow-lg">
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
              </svg>
            </a>
            <button
              (click)="addToCart(product, $event)"
              [class]="isInCart(product.id) ?
                'bg-green-500 text-white' :
                'bg-primary-600 text-white hover:bg-primary-700'"
              class="w-10 h-10 backdrop-blur-sm rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg">
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path *ngIf="!isInCart(product.id)" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"/>
                <path *ngIf="isInCart(product.id)" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- Product Info -->
        <div class="p-6">
          <h3 class="text-xl font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors duration-300">{{product.name}}</h3>
          <p class="text-gray-600 mb-4 line-clamp-2">{{product.description}}</p>

          <!-- Artist & Details -->
          <div class="space-y-2 mb-4">
            <div class="flex items-center text-sm text-gray-500">
              <svg class="h-4 w-4 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
              Artist: {{product.artist}}
            </div>
            <div class="flex items-center text-sm text-gray-500">
              <svg class="h-4 w-4 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"/>
              </svg>
              Size: {{product.dimensions}}
            </div>
          </div>

          <!-- Price and Action -->
          <div class="flex items-center justify-between">
            <div class="flex flex-col">
              <span class="text-2xl font-bold text-primary-600">{{formatPrice(product.price)}}</span>
              <span *ngIf="isInCart(product.id)" class="text-sm text-green-600 font-medium">✓ In Cart</span>
            </div>
            <a [routerLink]="['/products', product.slug]"
               class="bg-gradient-to-r from-primary-600 to-primary-700 text-white px-6 py-3 rounded-lg hover:from-primary-700 hover:to-primary-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl font-medium">
              View Details
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="filteredProducts.length === 0" class="text-center py-16">
      <div class="max-w-md mx-auto">
        <div class="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-full flex items-center justify-center">
          <svg class="h-12 w-12 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"/>
          </svg>
        </div>
        <h3 class="text-xl font-medium text-gray-900 mb-2">No products found</h3>
        <p class="text-gray-500 mb-6">Try selecting a different category or check back later for new arrivals.</p>
        <button
          (click)="filterByCategory('All')"
          class="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors duration-300 font-medium">
          View All Products
        </button>
      </div>
    </div>
  </div>
</section>

<!-- Call to Action Section -->
<section class="py-16 bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-600 relative overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 opacity-10">
    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <pattern id="cta-pattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
          <circle cx="30" cy="30" r="12" fill="none" stroke="#FFD700" stroke-width="1"/>
          <circle cx="30" cy="30" r="6" fill="none" stroke="#FFD700" stroke-width="1"/>
          <path d="M10,30 H20 M40,30 H50 M30,10 V20 M30,40 V50" stroke="#FFD700" stroke-width="1"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#cta-pattern)" />
    </svg>
  </div>

  <div class="container mx-auto px-4 text-center relative z-10">
    <div class="max-w-3xl mx-auto">
      <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
        Can't Find What You're Looking For?
      </h2>
      <p class="text-xl text-white/90 mb-8">
        We create custom Mithila art pieces tailored to your vision. Contact us to discuss your specific requirements and bring your ideas to life.
      </p>
      <div class="flex flex-wrap justify-center gap-4">
        <a routerLink="/contact"
           class="bg-white text-primary-600 px-8 py-4 rounded-full font-bold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
          Contact Us for Custom Orders
        </a>
        <a routerLink="/artists"
           class="bg-white/10 backdrop-blur-md text-white px-8 py-4 rounded-full font-bold hover:bg-white/20 transition-all duration-300 transform hover:scale-105 border border-white/30">
          Meet Our Artists
        </a>
      </div>
    </div>
  </div>
</section>
