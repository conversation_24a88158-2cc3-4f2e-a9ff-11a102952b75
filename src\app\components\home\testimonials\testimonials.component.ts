import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-testimonials',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './testimonials.component.html',
  styleUrl: './testimonials.component.css',
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('600ms ease-in', style({ opacity: 1 })),
      ]),
    ]),
    trigger('fadeSlide', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(20px)' }),
        animate('500ms ease-out', style({ opacity: 1, transform: 'translateY(0)' })),
      ]),
      transition(':leave', [
        style({ opacity: 1, transform: 'translateY(0)' }),
        animate('500ms ease-in', style({ opacity: 0, transform: 'translateY(-20px)' })),
      ]),
    ]),
  ]
})
export class TestimonialsComponent implements OnInit {
  currentSlide = 0;
  testimonials = [
    {
      id: 1,
      name: '<PERSON><PERSON>',
      location: 'Delhi, India',
      image: 'https://randomuser.me/api/portraits/women/32.jpg',
      rating: 5,
      quote: 'Unforgettable Maithili hospitality! The staff went above and beyond to make our pilgrimage to Janaki Temple special. The traditional decor and authentic food made us feel immersed in Mithila culture.'
    },
    {
      id: 2,
      name: 'John Williams',
      location: 'London, UK',
      image: 'https://randomuser.me/api/portraits/men/44.jpg',
      rating: 5,
      quote: 'As a cultural researcher, I was amazed by the attention to detail in preserving Mithila heritage. The Madhubani art workshops were enlightening, and the staff knowledge about local traditions was impressive.'
    },
    {
      id: 3,
      name: 'Rajesh Patel',
      location: 'Mumbai, India',
      image: 'https://randomuser.me/api/portraits/men/67.jpg',
      rating: 4,
      quote: 'A perfect blend of tradition and comfort. The rooms were clean and well-appointed while maintaining authentic Mithila aesthetics. The location near Janaki Temple made our family pilgrimage convenient and memorable.'
    }
  ];

  paused = false;

  ngOnInit() {
    this.startAutoSlide();
  }

  startAutoSlide() {
    setInterval(() => {
      if (!this.paused) {
        this.currentSlide = (this.currentSlide + 1) % this.testimonials.length;
      }
    }, 5000); // Change slide every 5 seconds
  }

  setCurrentSlide(index: number) {
    this.currentSlide = index;
  }

  pauseSlider() {
    this.paused = true;
  }

  resumeSlider() {
    this.paused = false;
  }
}
