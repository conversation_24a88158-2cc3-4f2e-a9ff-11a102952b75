/* Line clamp utilities for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Hover effects for product cards */
.product-card {
  transition: all 0.3s ease;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Custom button styles */
.btn-primary {
  @apply bg-primary-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 hover:bg-primary-700 hover:shadow-lg;
}

.btn-secondary {
  @apply bg-secondary-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 hover:bg-secondary-700 hover:shadow-lg;
}

/* Category filter button styles */
.category-btn {
  @apply px-6 py-3 rounded-full border border-primary-200 transition-all duration-300 font-medium;
}

.category-btn.active {
  @apply bg-primary-600 text-white border-primary-600;
}

.category-btn:not(.active) {
  @apply bg-white text-gray-700 hover:bg-primary-50 hover:border-primary-300;
}

/* Product image overlay effects */
.product-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-image-overlay {
  opacity: 1;
}

/* Badge styles */
.badge {
  @apply px-3 py-1 rounded-full text-sm font-medium;
}

.badge-featured {
  @apply bg-secondary-500 text-white;
}

.badge-in-stock {
  @apply bg-green-500 text-white;
}

.badge-out-of-stock {
  @apply bg-red-500 text-white;
}

.badge-category {
  @apply bg-white/90 backdrop-blur-sm text-primary-600;
}

/* Responsive grid adjustments */
@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1025px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

/* Loading state styles */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Custom scrollbar for category filters */
.category-filters::-webkit-scrollbar {
  height: 4px;
}

.category-filters::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.category-filters::-webkit-scrollbar-thumb {
  background: #c1440e;
  border-radius: 2px;
}

.category-filters::-webkit-scrollbar-thumb:hover {
  background: #a03a0c;
}
