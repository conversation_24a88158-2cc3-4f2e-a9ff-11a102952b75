.brush-stroke {
  position: absolute;
  z-index: 2;
  opacity: 0.7;
  mask-image: url("data:image/svg+xml,%3Csvg width='800' height='200' viewBox='0 0 800 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0,100 C100,30 200,150 300,100 C400,50 500,120 600,80 C700,40 800,90 800,100 L800,200 L0,200 Z' fill='black'/%3E%3C/svg%3E");
  mask-size: 100% 100%;
  mask-repeat: no-repeat;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg width='800' height='200' viewBox='0 0 800 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0,100 C100,30 200,150 300,100 C400,50 500,120 600,80 C700,40 800,90 800,100 L800,200 L0,200 Z' fill='black'/%3E%3C/svg%3E");
  -webkit-mask-size: 100% 100%;
  -webkit-mask-repeat: no-repeat;
}

.brush-stroke.primary {
  background-color: var(--primary-color, #C1440E);
}

.brush-stroke.secondary {
  background-color: var(--secondary-color, #F4B400);
}

.brush-stroke.accent {
  background-color: var(--accent-color, #008C8C);
}

.brush-stroke.success {
  background-color: var(--success-color, #3B945E);
}

.brush-stroke.top {
  top: 0;
  left: 0;
  transform: rotate(180deg);
}

.brush-stroke.bottom {
  bottom: 0;
  left: 0;
}

.brush-stroke.left {
  top: 0;
  left: 0;
  transform: rotate(90deg);
}

.brush-stroke.right {
  top: 0;
  right: 0;
  transform: rotate(270deg);
}
