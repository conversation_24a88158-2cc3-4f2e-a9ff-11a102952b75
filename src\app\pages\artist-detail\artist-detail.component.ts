import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../../components/shared/mithila-border/mithila-border.component';
import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';

@Component({
  selector: 'app-artist-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    SectionTitleComponent,
    MithilaSectionComponent,
    MithilaArtBackgroundComponent,
    MithilaBorderComponent,
    MithilaDecorativeElementComponent
  ],
  templateUrl: './artist-detail.component.html',
  styleUrl: './artist-detail.component.css'
})
export class ArtistDetailComponent implements OnInit {
  artistId: string = '';
  artist: any = null;
  artistArtworks: any[] = [];
  artistWorkshops: any[] = [];
  relatedArtists: any[] = [];

  // Artist data
  artists = [
    {
      id: '1',
      name: 'Sarita Devi',
      role: 'Master Artist & Founder',
      bio: 'With over 25 years of experience in traditional Mithila art, Sarita founded Mithilani Ghar to preserve and promote this unique cultural heritage. Her work has been exhibited internationally and she has received numerous awards for her contributions to Mithila art.',
      longBio: 'Sarita Devi began her artistic journey at the age of 12, learning traditional Mithila painting techniques from her grandmother. Born in a small village near Janakpur, she grew up surrounded by the rich cultural traditions of the Mithila region. After completing her formal education, she dedicated herself to mastering and preserving this ancient art form. In 2015, she established Mithilani Ghar with the vision of creating a space where artists could work, teach, and showcase their creations. Her paintings are characterized by intricate details, vibrant colors, and powerful storytelling that often explores themes of rural life, mythology, and women\'s experiences.',
      specialization: 'Traditional Mithila painting with natural pigments',
      achievements: [
        'National Award for Excellence in Traditional Arts (2018)',
        'Featured artist at the South Asian Art Festival, London (2019)',
        'Published in "Contemporary Folk Artists of South Asia" (2020)'
      ],
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      artworks: [1, 4, 8],
      workshops: true,
      featured: true,
      category: 'traditional',
      education: 'Self-taught under the guidance of village elders and master artists',
      experience: '25+ years of professional experience in Mithila art',
      exhibitions: [
        'South Asian Art Festival, London (2019)',
        'Traditional Arts Exhibition, Kathmandu (2018)',
        'Cultural Heritage Showcase, New Delhi (2017)'
      ],
      awards: [
        'National Award for Excellence in Traditional Arts (2018)',
        'Master Craftsperson Recognition, Ministry of Culture (2016)',
        'Regional Art Preservation Award (2015)'
      ],
      quote: 'Mithila art is not just a painting style; it\'s a visual language that tells the stories of our culture, traditions, and daily life. Through these intricate patterns and vibrant colors, we preserve our heritage for future generations.',
      socialMedia: {
        instagram: 'saritadevi_mithila',
        facebook: 'SaritaDeviArt',
        website: 'www.saritadevi-art.com'
      }
    },
    {
      id: '2',
      name: 'Ramesh Kumar',
      role: 'Contemporary Artist',
      bio: 'Ramesh blends traditional Mithila techniques with contemporary themes and styles. His innovative approach has brought Mithila art to new audiences while respecting its cultural roots.',
      longBio: 'Ramesh Kumar represents the new generation of Mithila artists who are expanding the boundaries of this traditional art form. After studying fine arts at Tribhuvan University in Kathmandu, he returned to his hometown of Janakpur with a mission to revitalize Mithila art for contemporary audiences. His work often incorporates urban landscapes, modern social issues, and experimental techniques while maintaining the distinctive aesthetic of Mithila painting. Through his art, Ramesh explores the intersection of tradition and modernity, creating pieces that resonate with both local communities and international art enthusiasts.',
      specialization: 'Contemporary Mithila art, mixed media',
      achievements: [
        'Young Artist Award, Nepal Art Council (2021)',
        'Artist residency at the Asian Cultural Center, Tokyo (2022)',
        'Solo exhibition "Tradition Reimagined" at Gallery Nepal, Kathmandu (2023)'
      ],
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      artworks: [2, 6, 9],
      workshops: true,
      featured: true,
      category: 'contemporary',
      education: 'BFA in Fine Arts, Tribhuvan University, Kathmandu',
      experience: '10 years of professional experience in contemporary Mithila art',
      exhibitions: [
        'Solo Exhibition: "Tradition Reimagined", Gallery Nepal, Kathmandu (2023)',
        'Asian Contemporary Art Fair, Tokyo (2022)',
        'Emerging Artists Showcase, Mumbai (2021)'
      ],
      awards: [
        'Young Artist Award, Nepal Art Council (2021)',
        'Innovation in Traditional Arts Grant (2020)',
        'Emerging Artist Fellowship (2019)'
      ],
      quote: 'My work is a conversation between the past and present. I draw from our rich artistic heritage while addressing contemporary themes and experimenting with new techniques. This dialogue keeps our traditions alive and relevant.',
      socialMedia: {
        instagram: 'ramesh_kumar_art',
        twitter: 'RameshKumarArt',
        website: 'www.rameshkumar-art.com'
      }
    },
    {
      id: '3',
      name: 'Anita Jha',
      role: 'Master Artist & Instructor',
      bio: 'Anita specializes in traditional Mithila painting techniques and leads our workshops and training programs for aspiring artists. Her work focuses on religious and mythological themes.',
      longBio: 'Anita Jha comes from a long line of Mithila artists and has been practicing this art form for over 20 years. She is particularly known for her detailed depictions of Hindu deities and mythological narratives, bringing ancient stories to life through vibrant colors and intricate patterns. As the lead instructor at Mithilani Ghar, Anita has trained hundreds of students in traditional Mithila painting techniques, with a special focus on preserving the authentic methods of pigment preparation and application. Her teaching philosophy emphasizes understanding the cultural context and symbolism behind each element in Mithila art.',
      specialization: 'Religious and mythological themes, traditional techniques',
      achievements: [
        'Master Craftsperson Award, Ministry of Culture (2017)',
        'Featured in the documentary "Living Traditions of Nepal" (2019)',
        'Conducted workshops at the National Museum, New Delhi (2020)'
      ],
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      artworks: [3, 5, 10],
      workshops: true,
      featured: true,
      category: 'traditional',
      education: 'Trained in family tradition of Mithila art; Advanced studies with Master Artist Ganga Devi',
      experience: '20+ years of creating and teaching traditional Mithila art',
      exhibitions: [
        'Sacred Art Exhibition, National Museum, New Delhi (2020)',
        'Women Artists of South Asia, Colombo (2019)',
        'Traditional Religious Art, Varanasi (2018)'
      ],
      awards: [
        'Master Craftsperson Award, Ministry of Culture (2017)',
        'Excellence in Art Education Award (2019)',
        'Cultural Preservation Recognition (2016)'
      ],
      quote: 'In each painting, I try to capture not just the visual beauty of our deities and myths, but their spiritual essence. When I teach, I pass on not only techniques but the sacred connection between artist, subject, and tradition.',
      socialMedia: {
        instagram: 'anita_jha_mithila',
        facebook: 'AnitaJhaArt',
        youtube: 'AnitaJhaMithilaArt'
      }
    },
    {
      id: '4',
      name: 'Sunil Yadav',
      role: 'Nature & Wildlife Artist',
      bio: 'Sunil\'s work celebrates the natural world through Mithila art, featuring detailed depictions of local flora and fauna. His pieces often carry environmental conservation messages.',
      longBio: 'Sunil Yadav has established himself as a specialist in nature-themed Mithila art, drawing inspiration from the rich biodiversity of the Terai region. With a background in environmental science and a passion for art, he creates pieces that not only showcase the beauty of nature but also raise awareness about environmental conservation. His distinctive style combines traditional Mithila patterns with scientifically accurate depictions of plants and animals, creating works that are both culturally significant and environmentally relevant. Through his art, Sunil hopes to inspire a deeper connection to nature and promote the protection of Nepal\'s natural heritage.',
      specialization: 'Flora and fauna, environmental themes',
      achievements: [
        'Environmental Art Prize, WWF Nepal (2022)',
        'Illustrated the book "Birds of the Terai: A Visual Guide" (2021)',
        'Exhibition "Natural Heritage in Folk Art" at the National Museum (2023)'
      ],
      image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
      artworks: [7, 11, 12],
      workshops: false,
      featured: true,
      category: 'emerging',
      education: 'BSc in Environmental Science, Tribhuvan University; Self-taught in Mithila art',
      experience: '8 years combining environmental themes with traditional Mithila art techniques',
      exhibitions: [
        'Exhibition "Natural Heritage in Folk Art", National Museum (2023)',
        'Environmental Art Showcase, Kathmandu (2022)',
        'Biodiversity Through Art, Chitwan (2021)'
      ],
      awards: [
        'Environmental Art Prize, WWF Nepal (2022)',
        'Conservation Through Art Grant (2021)',
        'Emerging Environmental Artist Award (2020)'
      ],
      quote: 'My art is a bridge between our cultural traditions and our natural environment. By depicting local wildlife and plants through Mithila art, I hope to remind people that our cultural heritage and natural heritage are deeply interconnected.',
      socialMedia: {
        instagram: 'sunil_nature_art',
        twitter: 'SunilYadavArt',
        facebook: 'SunilYadavNatureArt'
      }
    }
  ];

  // Artwork data
  artworks = [
    {
      id: 1,
      title: 'Celebration of Life',
      artist: 'Sarita Devi',
      artistId: '1',
      year: 2022,
      category: 'Traditional',
      description: 'A vibrant depiction of village life during festival season, showcasing the rich cultural traditions of Mithila.',
      longDescription: 'This intricate painting captures the joyous atmosphere of a traditional village festival in the Mithila region. The composition features multiple scenes of celebration, including ritual ceremonies, music, dance, and communal feasting. Traditional motifs and patterns frame the narrative scenes, while the vibrant color palette of reds, yellows, and blues creates a sense of energy and festivity. The painting exemplifies Sarita Devi\'s masterful use of traditional techniques and her deep understanding of cultural rituals and community life.',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      dimensions: '24 × 36 inches',
      medium: 'Natural pigments on handmade paper',
      price: 450,
      available: true,
      featured: true
    },
    {
      id: 2,
      title: 'Urban Mithila',
      artist: 'Ramesh Kumar',
      artistId: '2',
      year: 2023,
      category: 'Contemporary',
      description: 'A fusion of traditional Mithila art with urban landscapes, exploring the intersection of ancient and modern worlds.',
      longDescription: 'In this groundbreaking piece, Ramesh Kumar reimagines Mithila art in a contemporary urban context. The painting depicts a modern cityscape rendered in traditional Mithila style, with intricate patterns forming skyscrapers, vehicles, and city dwellers. Traditional motifs are cleverly integrated into modern elements, creating a visual dialogue between past and present. The color palette combines traditional earth tones with vibrant modern hues, symbolizing the evolution of cultural traditions in contemporary society. This work exemplifies Kumar\'s innovative approach to preserving cultural heritage while making it relevant to modern audiences.',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      dimensions: '24 × 36 inches',
      medium: 'Mixed media on canvas',
      price: 550,
      available: true,
      featured: true
    },
    {
      id: 3,
      title: 'Divine Feminine',
      artist: 'Anita Jha',
      artistId: '3',
      year: 2021,
      category: 'Religious',
      description: 'A powerful representation of the goddess Durga, showcasing the divine feminine energy through traditional Mithila symbolism.',
      longDescription: 'This masterful painting depicts the goddess Durga in traditional Mithila style, emphasizing her divine feminine power and protective nature. The central figure of the goddess is surrounded by intricate patterns and symbolic elements that represent her various attributes and powers. Anita Jha\'s expert use of traditional techniques is evident in the fine linework and balanced composition. The rich color palette of reds, yellows, and blacks creates a sense of spiritual intensity and divine presence. This work exemplifies Jha\'s deep knowledge of Hindu mythology and her ability to convey spiritual concepts through visual storytelling.',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      dimensions: '30 × 40 inches',
      medium: 'Natural pigments on handmade paper',
      price: 600,
      available: false,
      featured: true
    },
    {
      id: 4,
      title: 'Kohbar Ghar',
      artist: 'Sarita Devi',
      artistId: '1',
      year: 2020,
      category: 'Traditional',
      description: 'A traditional wedding chamber painting, filled with auspicious symbols for fertility, prosperity, and marital harmony.',
      longDescription: 'This traditional Kohbar painting is created to bless the marriage chamber of newlyweds. The composition features a central lotus motif symbolizing fertility, surrounded by pairs of fish, peacocks, turtles, and other symbols representing love, fertility, and prosperity. The bamboo grove represents the male principle, while the lotus pond represents the female principle. Sarita Devi\'s masterful execution of this traditional form demonstrates her deep understanding of Mithila cultural practices and symbolism. The painting is created using natural pigments in the traditional color palette of red, black, and yellow, applied with remarkable precision and detail.',
      image: 'https://i.etsystatic.com/43638819/r/il/9b1cc7/5978434663/il_794xN.5978434663_hk13.jpg',
      dimensions: '36 × 48 inches',
      medium: 'Natural pigments on handmade paper',
      price: 750,
      available: true,
      featured: false
    },
    {
      id: 5,
      title: 'Krishna Leela',
      artist: 'Anita Jha',
      artistId: '3',
      year: 2022,
      category: 'Religious',
      description: 'A series of scenes depicting the playful adventures of Lord Krishna, rendered in traditional Mithila style.',
      longDescription: 'This elaborate narrative painting depicts various episodes from the life of Lord Krishna, focusing on his playful and mischievous nature. The composition is divided into multiple scenes showing Krishna stealing butter, playing with gopis, lifting Mount Govardhan, and playing his flute. Each scene is framed with traditional Mithila patterns and motifs, creating a harmonious visual rhythm throughout the piece. Anita Jha\'s expertise in religious themes is evident in her accurate depiction of mythological narratives and her attention to symbolic details. The vibrant colors and flowing lines create a sense of divine joy and celebration that is central to Krishna worship.',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      dimensions: '24 × 36 inches',
      medium: 'Natural pigments on handmade paper',
      price: 500,
      available: true,
      featured: false
    },
    {
      id: 6,
      title: 'Digital Diaspora',
      artist: 'Ramesh Kumar',
      artistId: '2',
      year: 2023,
      category: 'Contemporary',
      description: 'An exploration of how traditional cultural elements persist and transform in the digital age and global diaspora.',
      longDescription: 'This innovative piece explores the theme of cultural identity in the digital age and global diaspora. Traditional Mithila motifs and patterns are integrated with representations of digital technology, social media, and global connectivity. The composition creates a visual narrative about how cultural traditions evolve and adapt across geographical boundaries and technological platforms. Ramesh Kumar\'s experimental approach is evident in his use of mixed media, combining traditional natural pigments with modern acrylic paints and digital printing techniques. The color palette bridges traditional earth tones with the bright colors associated with digital interfaces, creating a visual dialogue between past and present.',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      dimensions: '30 × 30 inches',
      medium: 'Mixed media on canvas',
      price: 600,
      available: true,
      featured: false
    },
    {
      id: 7,
      title: 'Birds of Terai',
      artist: 'Sunil Yadav',
      artistId: '4',
      year: 2022,
      category: 'Nature',
      description: 'A celebration of the diverse bird species found in Nepal\'s Terai region, combining scientific accuracy with traditional art.',
      longDescription: 'This detailed painting showcases the rich avian biodiversity of Nepal\'s Terai region. Sunil Yadav combines his knowledge of environmental science with traditional Mithila art techniques to create scientifically accurate depictions of local bird species within a framework of traditional patterns and motifs. The composition features over twenty different bird species in their natural habitats, each rendered with careful attention to their distinctive features and behaviors. The background incorporates traditional Mithila patterns that evoke the landscapes of the Terai region. This piece exemplifies Yadav\'s unique approach of using traditional art forms to raise awareness about local biodiversity and conservation issues.',
      image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
      dimensions: '24 × 36 inches',
      medium: 'Natural pigments and acrylic on handmade paper',
      price: 400,
      available: true,
      featured: true
    }
  ];

  // Workshop data
  workshops = [
    {
      id: 1,
      title: 'Introduction to Mithila Art',
      artist: 'Sarita Devi',
      artistId: '1',
      date: 'June 15-16, 2024',
      time: '10:00 AM - 4:00 PM',
      location: 'Main Workshop, Mithilani Ghar',
      description: 'A beginner-friendly workshop covering the basic techniques, motifs, and cultural context of traditional Mithila painting.',
      longDescription: 'This comprehensive two-day workshop introduces participants to the fundamentals of Mithila art. Day one focuses on understanding the cultural context, history, and symbolism of Mithila painting, followed by practice with basic line work and geometric patterns. Day two covers color preparation using natural pigments, traditional motifs, and composition principles. Participants will complete a small Mithila painting by the end of the workshop. All materials are provided, and no prior experience is necessary. This workshop is ideal for beginners who want to understand both the techniques and cultural significance of this ancient art form.',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      price: 2500,
      spaces: 15,
      spacesLeft: 8
    },
    {
      id: 2,
      title: 'Contemporary Mithila Techniques',
      artist: 'Ramesh Kumar',
      artistId: '2',
      date: 'July 8-10, 2024',
      time: '1:00 PM - 5:00 PM',
      location: 'Studio 2, Mithilani Ghar',
      description: 'Explore innovative approaches to Mithila art, including mixed media techniques and contemporary themes while respecting traditional aesthetics.',
      longDescription: 'This three-day advanced workshop explores how traditional Mithila art techniques can be adapted for contemporary expression. Participants will learn to combine traditional methods with modern materials and themes while maintaining the distinctive aesthetic of Mithila art. The workshop covers experimental techniques including mixed media applications, incorporation of modern subjects, and adaptation of traditional patterns for contemporary narratives. Participants should have basic familiarity with Mithila art techniques. All specialized materials are provided. This workshop is ideal for artists looking to develop their own unique style within the Mithila tradition.',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      price: 3500,
      spaces: 12,
      spacesLeft: 5
    },
    {
      id: 3,
      title: 'Mythological Themes in Mithila Art',
      artist: 'Anita Jha',
      artistId: '3',
      date: 'August 20-22, 2024',
      time: '9:00 AM - 3:00 PM',
      location: 'Main Workshop, Mithilani Ghar',
      description: 'Learn to depict Hindu deities and mythological narratives using traditional Mithila painting techniques and symbolism.',
      longDescription: 'This specialized three-day workshop focuses on the representation of Hindu deities and mythological narratives in Mithila art. Participants will learn about the iconography, symbolism, and traditional compositions used to depict various deities and stories. The workshop covers the specific patterns, colors, and attributes associated with different gods and goddesses, as well as techniques for creating narrative sequences. Participants will work on creating a detailed mythological painting under Anita Jha\'s expert guidance. Basic familiarity with Mithila painting techniques is recommended. All materials are provided. This workshop is ideal for those interested in the spiritual and religious aspects of Mithila art tradition.',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      price: 3000,
      spaces: 15,
      spacesLeft: 10
    }
  ];

  constructor(private route: ActivatedRoute) {
    this.route.params.subscribe(params => {
      this.artistId = params['id'];
      this.loadArtistData();
    });
  }

  ngOnInit(): void {
    this.loadArtistData();
  }

  loadArtistData(): void {
    // Find the artist by ID
    this.artist = this.artists.find(a => a.id === this.artistId);

    if (this.artist) {
      // Get artworks by this artist
      this.artistArtworks = this.artworks.filter(artwork => artwork.artistId === this.artistId);

      // Get workshops by this artist
      this.artistWorkshops = this.workshops.filter(workshop => workshop.artistId === this.artistId);

      // Get related artists (same category but not the current artist)
      this.relatedArtists = this.artists.filter(a =>
        a.category === this.artist.category && a.id !== this.artistId
      ).slice(0, 2); // Limit to 2 related artists
    }
  }
}
