<header class="bg-white shadow-sm border-b border-gray-100">
  <div class="container mx-auto px-4">
    <div class="flex justify-between items-center py-4">
      <!-- Logo -->
      <a routerLink="/" class="flex items-center space-x-3">
        <div class="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
          <span class="text-white font-bold text-lg">M</span>
        </div>
        <span class="text-xl font-bold text-gray-900"><PERSON><PERSON><PERSON></span>
      </a>

      <!-- Desktop Navigation -->
      <nav class="hidden md:flex items-center space-x-8">
        <a routerLink="/" routerLinkActive="text-primary-600" [routerLinkActiveOptions]="{exact: true}"
          class="text-gray-600 hover:text-primary-600 transition-colors">Home</a>
        <a routerLink="/products" routerLinkActive="text-primary-600"
          class="text-gray-600 hover:text-primary-600 transition-colors">Products</a>
        <a routerLink="/gallery" routerLinkActive="text-primary-600"
          class="text-gray-600 hover:text-primary-600 transition-colors">Gallery</a>
        <a routerLink="/artists" routerLinkActive="text-primary-600"
          class="text-gray-600 hover:text-primary-600 transition-colors">Artists</a>
        <a routerLink="/about" routerLinkActive="text-primary-600"
          class="text-gray-600 hover:text-primary-600 transition-colors">About</a>
        <a routerLink="/contact" routerLinkActive="text-primary-600"
          class="text-gray-600 hover:text-primary-600 transition-colors">Contact</a>

        <!-- Cart Icon -->
        <a routerLink="/cart" class="relative p-2 text-gray-600 hover:text-primary-600 transition-colors">
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"/>
          </svg>
          <span *ngIf="cartItemCount > 0"
                class="absolute -top-1 -right-1 bg-primary-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {{cartItemCount}}
          </span>
        </a>
      </nav>

      <!-- Mobile menu button and cart -->
      <div class="md:hidden flex items-center space-x-4">
        <!-- Mobile Cart Icon -->
        <a routerLink="/cart" class="relative p-2 text-gray-600 hover:text-primary-600 transition-colors">
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"/>
          </svg>
          <span *ngIf="cartItemCount > 0"
                class="absolute -top-1 -right-1 bg-primary-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {{cartItemCount}}
          </span>
        </a>

        <button type="button" (click)="toggleMenu()" class="text-gray-600 hover:text-primary-600 focus:outline-none">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path *ngIf="!isMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            <path *ngIf="isMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Navigation -->
    <div *ngIf="isMenuOpen" class="md:hidden py-4 border-t border-gray-100">
      <nav class="flex flex-col space-y-3">
        <a routerLink="/" routerLinkActive="text-primary-600" [routerLinkActiveOptions]="{exact: true}"
          class="text-gray-600 hover:text-primary-600 transition-colors py-2">Home</a>
        <a routerLink="/products" routerLinkActive="text-primary-600"
          class="text-gray-600 hover:text-primary-600 transition-colors py-2">Products</a>
        <a routerLink="/gallery" routerLinkActive="text-primary-600"
          class="text-gray-600 hover:text-primary-600 transition-colors py-2">Gallery</a>
        <a routerLink="/artists" routerLinkActive="text-primary-600"
          class="text-gray-600 hover:text-primary-600 transition-colors py-2">Artists</a>
        <a routerLink="/about" routerLinkActive="text-primary-600"
          class="text-gray-600 hover:text-primary-600 transition-colors py-2">About</a>
        <a routerLink="/contact" routerLinkActive="text-primary-600"
          class="text-gray-600 hover:text-primary-600 transition-colors py-2">Contact</a>
      </nav>
    </div>
  </div>
</header>
