<header class="bg-white shadow-md">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center py-4">
      <!-- Logo -->
      <div class="flex items-center">
        <a routerLink="/" class="flex items-center">
          <img src="https://scontent.fktm7-1.fna.fbcdn.net/v/t39.30808-1/385774439_122127428114027028_7855394761270808633_n.jpg?_nc_cat=109&ccb=1-7&_nc_sid=2d3e12&_nc_ohc=PB-osu-lkYcQ7kNvwFNzYN2&_nc_oc=AdkotyFZRYEzQacojl3JuEhYCPIpckyjFCIpTmNyP3rVDG6oDSEhjvAfrkRzrZUJoHc&_nc_zt=24&_nc_ht=scontent.fktm7-1.fna&_nc_gid=EuuP8j_Yu48e-8-9amlTDQ&oh=00_AfIyKCZ962bqhJ8sZrexnY9iHICOSm_t3hwK7h1tBIGELw&oe=6834DCDA" alt="Mithilani Ghar Logo" class="h-12 w-auto">
          <span class="ml-3 text-xl font-display font-semibold text-mithila-red">Mithilani Ghar</span>
        </a>
      </div>

      <!-- Desktop Navigation -->
      <nav class="hidden md:flex space-x-8">
        <a routerLink="/" routerLinkActive="text-primary-600 font-medium" [routerLinkActiveOptions]="{exact: true}"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Home</a>
        <a routerLink="/about" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">About Us</a>
        <a routerLink="/gallery" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Gallery</a>
        <a routerLink="/artists" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Artists</a>
        <a routerLink="/events" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Events</a>
        <a routerLink="/blog" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Blog</a>
        <a routerLink="/contact" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Contact</a>
      </nav>

      <!-- Mobile menu button -->
      <div class="md:hidden">
        <button type="button" (click)="toggleMenu()" class="text-gray-700 hover:text-primary-600 focus:outline-none">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path *ngIf="!isMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            <path *ngIf="isMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Navigation -->
    <div *ngIf="isMenuOpen" class="md:hidden py-4 border-t border-gray-200">
      <nav class="flex flex-col space-y-4">
        <a routerLink="/" routerLinkActive="text-primary-600 font-medium" [routerLinkActiveOptions]="{exact: true}"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Home</a>
        <a routerLink="/about" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">About Us</a>
        <a routerLink="/gallery" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Gallery</a>
        <a routerLink="/artists" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Artists</a>
        <a routerLink="/events" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Events</a>
        <a routerLink="/blog" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Blog</a>
        <a routerLink="/contact" routerLinkActive="text-primary-600 font-medium"
          class="text-gray-700 hover:text-primary-600 transition-colors duration-200">Contact</a>
      </nav>
    </div>
  </div>
</header>
