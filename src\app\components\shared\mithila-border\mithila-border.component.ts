import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-mithila-border',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="absolute {{position}} pointer-events-none overflow-hidden">
      <svg [attr.width]="width" [attr.height]="height" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="border-gradient-{{uniqueId}}" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" [attr.stop-color]="primaryColor" stop-opacity="0.7">
              <animate attributeName="stop-opacity" values="0.7;0.3;0.7" dur="5s" repeatCount="indefinite" />
            </stop>
            <stop offset="50%" stop-color="#FFFFFF" stop-opacity="0.9">
              <animate attributeName="stop-opacity" values="0.9;0.5;0.9" dur="5s" repeatCount="indefinite" />
            </stop>
            <stop offset="100%" [attr.stop-color]="secondaryColor" stop-opacity="0.7">
              <animate attributeName="stop-opacity" values="0.7;0.3;0.7" dur="5s" repeatCount="indefinite" />
            </stop>
          </linearGradient>
        </defs>
        
        <!-- Border Pattern -->
        <g *ngIf="type === 'top' || type === 'full'">
          <path d="M0,10 H100" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="1" fill="none" />
          <path d="M0,5 H100" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="0.5" fill="none" />
          
          <!-- Decorative Elements -->
          <g *ngFor="let i of [10, 30, 50, 70, 90]">
            <circle [attr.cx]="i" cy="7.5" r="2.5" fill="none" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="0.5">
              <animate attributeName="r" [attr.values]="'2.5;3;2.5'" dur="3s" repeatCount="indefinite" [attr.begin]="i/30 + 's'" />
            </circle>
            <path [attr.d]="'M' + i + ',10 L' + i + ',15'" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="0.5" fill="none" />
          </g>
        </g>
        
        <g *ngIf="type === 'bottom' || type === 'full'">
          <path d="M0,90 H100" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="1" fill="none" />
          <path d="M0,95 H100" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="0.5" fill="none" />
          
          <!-- Decorative Elements -->
          <g *ngFor="let i of [10, 30, 50, 70, 90]">
            <circle [attr.cx]="i" cy="92.5" r="2.5" fill="none" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="0.5">
              <animate attributeName="r" [attr.values]="'2.5;3;2.5'" dur="3s" repeatCount="indefinite" [attr.begin]="i/30 + 's'" />
            </circle>
            <path [attr.d]="'M' + i + ',90 L' + i + ',85'" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="0.5" fill="none" />
          </g>
        </g>
        
        <g *ngIf="type === 'left' || type === 'full'">
          <path d="M10,0 V100" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="1" fill="none" />
          <path d="M5,0 V100" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="0.5" fill="none" />
          
          <!-- Decorative Elements -->
          <g *ngFor="let i of [10, 30, 50, 70, 90]">
            <circle cx="7.5" [attr.cy]="i" r="2.5" fill="none" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="0.5">
              <animate attributeName="r" [attr.values]="'2.5;3;2.5'" dur="3s" repeatCount="indefinite" [attr.begin]="i/30 + 's'" />
            </circle>
            <path [attr.d]="'M10,' + i + ' L15,' + i" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="0.5" fill="none" />
          </g>
        </g>
        
        <g *ngIf="type === 'right' || type === 'full'">
          <path d="M90,0 V100" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="1" fill="none" />
          <path d="M95,0 V100" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="0.5" fill="none" />
          
          <!-- Decorative Elements -->
          <g *ngFor="let i of [10, 30, 50, 70, 90]">
            <circle cx="92.5" [attr.cy]="i" r="2.5" fill="none" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="0.5">
              <animate attributeName="r" [attr.values]="'2.5;3;2.5'" dur="3s" repeatCount="indefinite" [attr.begin]="i/30 + 's'" />
            </circle>
            <path [attr.d]="'M90,' + i + ' L85,' + i" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="0.5" fill="none" />
          </g>
        </g>
        
        <!-- Corner Decorations -->
        <g *ngIf="type === 'full' || type === 'corners'">
          <!-- Top Left Corner -->
          <path d="M0,20 Q0,0 20,0" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="1" fill="none" />
          <circle cx="10" cy="10" r="5" fill="none" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="0.5">
            <animate attributeName="r" values="5;6;5" dur="3s" repeatCount="indefinite" />
          </circle>
          
          <!-- Top Right Corner -->
          <path d="M80,0 Q100,0 100,20" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="1" fill="none" />
          <circle cx="90" cy="10" r="5" fill="none" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="0.5">
            <animate attributeName="r" values="5;6;5" dur="3s" repeatCount="indefinite" delay="0.5s" />
          </circle>
          
          <!-- Bottom Left Corner -->
          <path d="M0,80 Q0,100 20,100" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="1" fill="none" />
          <circle cx="10" cy="90" r="5" fill="none" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="0.5">
            <animate attributeName="r" values="5;6;5" dur="3s" repeatCount="indefinite" delay="1s" />
          </circle>
          
          <!-- Bottom Right Corner -->
          <path d="M80,100 Q100,100 100,80" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="1" fill="none" />
          <circle cx="90" cy="90" r="5" fill="none" [attr.stroke]="'url(#border-gradient-' + uniqueId + ')'" stroke-width="0.5">
            <animate attributeName="r" values="5;6;5" dur="3s" repeatCount="indefinite" delay="1.5s" />
          </circle>
        </g>
      </svg>
    </div>
  `,
  styles: []
})
export class MithilaBorderComponent {
  @Input() primaryColor: string = '#C1440E'; // Terracotta Red
  @Input() secondaryColor: string = '#F4B400'; // Sunflower Yellow
  @Input() type: 'top' | 'bottom' | 'left' | 'right' | 'full' | 'corners' = 'full';
  @Input() position: string = 'inset-0'; // CSS position classes
  @Input() width: string = '100%';
  @Input() height: string = '100%';
  
  // Generate a unique ID for SVG elements
  uniqueId: string = Math.random().toString(36).substring(2, 9);
}
