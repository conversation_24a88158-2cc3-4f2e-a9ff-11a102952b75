import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-floating-words',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './floating-words.component.html',
  styleUrl: './floating-words.component.css'
})
export class FloatingWordsComponent {
  words = [
    { text: 'Art', size: 'text-5xl', opacity: '0.7', top: '15%', left: '10%', delay: '0s' },
    { text: 'Mithila', size: 'text-4xl', opacity: '0.8', top: '25%', left: '75%', delay: '0.5s' },
    { text: 'Culture', size: 'text-3xl', opacity: '0.6', top: '60%', left: '20%', delay: '1s' },
    { text: 'Heritage', size: 'text-2xl', opacity: '0.7', top: '40%', left: '85%', delay: '1.5s' },
    { text: 'Tradition', size: 'text-3xl', opacity: '0.5', top: '70%', left: '60%', delay: '2s' },
    { text: 'Painting', size: 'text-4xl', opacity: '0.8', top: '80%', left: '30%', delay: '2.5s' },
    { text: 'Crafts', size: 'text-2xl', opacity: '0.6', top: '30%', left: '40%', delay: '3s' },
    { text: 'Janakpur', size: 'text-3xl', opacity: '0.7', top: '50%', left: '5%', delay: '3.5s' },
    { text: 'Folk', size: 'text-2xl', opacity: '0.5', top: '20%', left: '60%', delay: '4s' },
    { text: 'Artistic', size: 'text-3xl', opacity: '0.6', top: '65%', left: '80%', delay: '4.5s' }
  ];
}
