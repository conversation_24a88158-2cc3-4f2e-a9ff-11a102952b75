<section class="py-16 md:py-24 bg-white">
  <div class="container mx-auto px-4">
    <!-- Section Title -->
    <div class="text-center mb-12" @fadeIn>
      <h2 class="text-3xl md:text-4xl font-heading font-semibold text-primary-500 mb-4">Immerse in Mithila Culture</h2>
      <p class="text-lg text-gray-600 max-w-3xl mx-auto">
        Experience the rich cultural heritage of Mithila through art, cuisine, and local attractions.
      </p>
    </div>
    
    <!-- Carousel -->
    <div class="relative max-w-5xl mx-auto" @slideIn>
      <!-- Slides -->
      <div class="overflow-hidden rounded-lg shadow-xl">
        <div class="relative">
          <!-- Slide Images -->
          <div *ngFor="let highlight of highlights; let i = index" 
               class="transition-opacity duration-1000 ease-in-out"
               [class.opacity-0]="i !== currentSlide"
               [class.hidden]="i !== currentSlide">
            <!-- Image -->
            <div class="relative h-96">
              <img [src]="highlight.image" [alt]="highlight.title" class="w-full h-full object-cover">
              
              <!-- Overlay Gradient -->
              <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
              
              <!-- Content -->
              <div class="absolute bottom-0 left-0 right-0 p-8 text-white">
                <!-- Madhubani Border Animation -->
                <div class="madhubani-border"></div>
                
                <h3 class="text-2xl md:text-3xl font-heading font-semibold mb-3">{{highlight.title}}</h3>
                <p class="text-white/90 mb-4 max-w-2xl">{{highlight.description}}</p>
                <a [routerLink]="highlight.link" class="btn btn-secondary text-background-dark inline-flex items-center">
                  <span>Learn More</span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Navigation Dots -->
      <div class="flex justify-center mt-6 space-x-2">
        <button *ngFor="let highlight of highlights; let i = index" 
                (click)="setCurrentSlide(i)"
                class="w-3 h-3 rounded-full transition-colors duration-300"
                [class.bg-primary-500]="i === currentSlide"
                [class.bg-gray-300]="i !== currentSlide">
        </button>
      </div>
      
      <!-- Navigation Arrows -->
      <button (click)="setCurrentSlide((currentSlide - 1 + highlights.length) % highlights.length)" 
              class="absolute top-1/2 left-4 transform -translate-y-1/2 bg-white/30 hover:bg-white/50 text-white rounded-full p-2 backdrop-blur-sm transition-colors duration-300">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      <button (click)="setCurrentSlide((currentSlide + 1) % highlights.length)" 
              class="absolute top-1/2 right-4 transform -translate-y-1/2 bg-white/30 hover:bg-white/50 text-white rounded-full p-2 backdrop-blur-sm transition-colors duration-300">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  </div>
</section>
