<!-- Enhanced Hero Section -->
<div class="relative h-[90vh] overflow-hidden">
  <!-- Background Image with Parallax Effect -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat transform transition-transform duration-10000 hover:scale-105"
       style="background-image: url('https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg');">
  </div>

  <!-- Animated Gradient Overlay -->
  <div class="absolute inset-0 bg-gradient-to-br from-primary-600/90 via-primary-500/80 to-secondary-600/90"></div>

  <!-- <PERSON><PERSON><PERSON> Background -->
  <div class="absolute inset-0 opacity-20">
    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <pattern id="hero-pattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
          <circle cx="30" cy="30" r="12" fill="none" stroke="#FFD700" stroke-width="1"/>
          <circle cx="30" cy="30" r="6" fill="none" stroke="#FFD700" stroke-width="1"/>
          <path d="M10,30 H20 M40,30 H50 M30,10 V20 M30,40 V50" stroke="#FFD700" stroke-width="1"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#hero-pattern)" />
    </svg>
  </div>

  <!-- Floating Elements -->
  <div class="absolute inset-0 overflow-hidden opacity-30">
    <div class="absolute w-32 h-32 rounded-full top-[20%] left-[15%] animate-float-slow">
      <div class="absolute inset-0 rounded-full border-2 border-white/30 animate-spin-slow"></div>
    </div>
    <div class="absolute w-20 h-20 rounded-full top-[60%] right-[20%] animate-float-medium">
      <div class="absolute inset-0 bg-white/10 backdrop-blur-sm rounded-full"></div>
    </div>
    <div class="absolute w-16 h-16 rounded-full bottom-[30%] left-[10%] animate-float-fast">
      <div class="absolute inset-0 bg-secondary-400/30 rounded-full"></div>
    </div>
  </div>

  <!-- Hero Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <div class="max-w-5xl mx-auto">
      <!-- Animated Logo -->
      <div class="mb-8 relative">
        <div class="inline-block p-4 rounded-full bg-white/10 backdrop-blur-md border border-white/30 shadow-2xl animate-float-slow">
          <div class="w-20 h-20 bg-gradient-to-br from-secondary-400 to-secondary-600 rounded-full flex items-center justify-center">
            <span class="text-white font-bold text-2xl">M</span>
          </div>
        </div>
      </div>

      <h1 class="text-5xl md:text-7xl font-bold text-white mb-6 font-display animate-fade-in-up">
        Mithilani Ghar
      </h1>

      <p class="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto animate-fade-in-up animation-delay-300">
        Preserving and Promoting the Rich Artistic Heritage of Mithila
      </p>

      <!-- Enhanced Navigation Buttons -->
      <div class="flex flex-wrap justify-center gap-4 mb-12 animate-fade-in-up animation-delay-600">
        <a routerLink="/products"
           class="group relative px-6 py-3 bg-white/10 backdrop-blur-md hover:bg-secondary-500/80 text-white rounded-lg transition-all duration-500 transform hover:-translate-y-2 hover:shadow-xl flex items-center border border-white/20 overflow-hidden">
          <div class="absolute inset-0 bg-gradient-to-r from-secondary-500/0 via-secondary-500/30 to-secondary-500/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
          <svg class="h-5 w-5 mr-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
          </svg>
          <span class="relative z-10 font-medium">Shop Products</span>
        </a>

        <a routerLink="/gallery"
           class="group relative px-6 py-3 bg-white/10 backdrop-blur-md hover:bg-secondary-500/80 text-white rounded-lg transition-all duration-500 transform hover:-translate-y-2 hover:shadow-xl flex items-center border border-white/20 overflow-hidden">
          <div class="absolute inset-0 bg-gradient-to-r from-secondary-500/0 via-secondary-500/30 to-secondary-500/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
          <svg class="h-5 w-5 mr-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
          </svg>
          <span class="relative z-10 font-medium">Explore Gallery</span>
        </a>

        <a routerLink="/contact"
           class="group relative px-6 py-3 bg-white/10 backdrop-blur-md hover:bg-secondary-500/80 text-white rounded-lg transition-all duration-500 transform hover:-translate-y-2 hover:shadow-xl flex items-center border border-white/20 overflow-hidden">
          <div class="absolute inset-0 bg-gradient-to-r from-secondary-500/0 via-secondary-500/30 to-secondary-500/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
          <svg class="h-5 w-5 mr-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
          </svg>
          <span class="relative z-10 font-medium">Contact Us</span>
        </a>
      </div>

      <!-- Scroll Indicator -->
      <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div class="flex flex-col items-center">
          <span class="text-white/80 text-sm mb-2">Scroll Down</span>
          <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
          </svg>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- About Section with Enhanced Floating Design -->
<app-mithila-section
  primaryColor="#C1440E"
  secondaryColor="#F4B400"
  backgroundGradient="from-primary-50 via-background-light to-secondary-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <div class="container relative" @fadeIn>
    <!-- Section Header with Animation -->
    <app-section-title
      title="Welcome to Mithilani Ghar"
      subtitle="A sanctuary of Mithila art and culture in the heart of Janakpur"
    ></app-section-title>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 items-center">
      <!-- Left Column: Image with Decorative Border -->
      <div @scaleIn class="relative">
        <!-- Main Image -->
        <div class="rounded-lg overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-rotate-1 hover:scale-105 relative z-10">
          <!-- Gradient Border -->
          <div class="absolute -inset-0.5 bg-gradient-to-br from-primary-300 via-secondary-300 to-accent-300 rounded-lg blur-sm animate-border-gradient"></div>

          <div class="relative rounded-lg overflow-hidden">
            <img src="https://i.etsystatic.com/43638819/r/il/9b1cc7/5978434663/il_794xN.5978434663_hk13.jpg" alt="Mithilani Ghar" class="w-full h-auto transition-transform duration-700 hover:scale-105">

            <!-- Overlay Gradient -->
            <div class="absolute inset-0 bg-gradient-to-t from-primary-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
        </div>

        <!-- Decorative Elements -->
        <div class="absolute -bottom-6 -right-6 w-full h-full border-4 border-dashed border-primary-200 rounded-lg z-0 animate-pulse-slow"></div>

        <!-- Floating Badge -->
        <div class="absolute -top-5 -right-5 bg-white shadow-lg rounded-full p-4 z-20 transform rotate-12 hover:rotate-0 transition-transform duration-300 animate-float-medium">
          <div class="absolute inset-0 rounded-full bg-gradient-to-br from-secondary-100 to-secondary-200 animate-spin-slow"></div>
          <img src="https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg" alt="Mithila Art" class="h-16 w-16 rounded-full object-cover relative z-10">
        </div>
      </div>

      <!-- Right Column: Content -->
      <div class="space-y-6">
        <!-- Feature Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-6 sm:mb-8" @staggerIn>
          <!-- Card 1: Art Gallery -->
          <div class="group bg-white p-3 sm:p-4 md:p-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border-l-4 border-primary-500 transform hover:-translate-y-1 hover:border-l-6 relative overflow-hidden">
            <!-- Card Background Animation -->
            <div class="absolute inset-0 bg-gradient-to-r from-primary-50/0 via-primary-50/50 to-primary-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

            <div class="flex items-start relative z-10">
              <div class="bg-primary-100 p-3 rounded-full mr-4 group-hover:bg-primary-200 transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h3 class="font-bold text-gray-900 mb-1 group-hover:text-primary-600 transition-colors duration-300">Art Gallery</h3>
                <p class="text-gray-600 text-sm">Showcasing authentic Mithila paintings and crafts</p>
              </div>
            </div>
          </div>

          <!-- Card 2: Craft Store -->
          <div class="group bg-white p-3 sm:p-4 md:p-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border-l-4 border-secondary-500 transform hover:-translate-y-1 hover:border-l-6 relative overflow-hidden">
            <!-- Card Background Animation -->
            <div class="absolute inset-0 bg-gradient-to-r from-secondary-50/0 via-secondary-50/50 to-secondary-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

            <div class="flex items-start relative z-10">
              <div class="bg-secondary-100 p-3 rounded-full mr-4 group-hover:bg-secondary-200 transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-secondary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <div>
                <h3 class="font-bold text-gray-900 mb-1 group-hover:text-secondary-600 transition-colors duration-300">Craft Store</h3>
                <p class="text-gray-600 text-sm">Handmade products by skilled local artisans</p>
              </div>
            </div>
          </div>

          <!-- Card 3: Training Center -->
          <div class="group bg-white p-3 sm:p-4 md:p-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border-l-4 border-accent-500 transform hover:-translate-y-1 hover:border-l-6 relative overflow-hidden">
            <!-- Card Background Animation -->
            <div class="absolute inset-0 bg-gradient-to-r from-accent-50/0 via-accent-50/50 to-accent-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

            <div class="flex items-start relative z-10">
              <div class="bg-accent-100 p-3 rounded-full mr-4 group-hover:bg-accent-200 transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-accent-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M12 14l9-5-9-5-9 5 9 5z" />
                  <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                </svg>
              </div>
              <div>
                <h3 class="font-bold text-gray-900 mb-1 group-hover:text-accent-600 transition-colors duration-300">Training Center</h3>
                <p class="text-gray-600 text-sm">Learn traditional Mithila art techniques</p>
              </div>
            </div>
          </div>

          <!-- Card 4: Cultural Hub -->
          <div class="group bg-white p-3 sm:p-4 md:p-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border-l-4 border-mithila-red transform hover:-translate-y-1 hover:border-l-6 relative overflow-hidden">
            <!-- Card Background Animation -->
            <div class="absolute inset-0 bg-gradient-to-r from-red-50/0 via-red-50/50 to-red-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

            <div class="flex items-start relative z-10">
              <div class="bg-red-100 p-3 rounded-full mr-4 group-hover:bg-red-200 transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-mithila-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 class="font-bold text-gray-900 mb-1 group-hover:text-mithila-red transition-colors duration-300">Cultural Hub</h3>
                <p class="text-gray-600 text-sm">Celebrating the heritage of Mithila region</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Main Content -->
        <div class="space-y-4" @fadeSlideIn>
          <p class="text-lg text-gray-700 relative">
            <span class="absolute -left-4 top-0 h-full w-1 bg-gradient-to-b from-primary-300/0 via-primary-300 to-primary-300/0"></span>
            Mithilani Ghar is a dedicated hub for promoting and preserving the rich artistic heritage of the Mithila region. Located in Janakpur, Nepal, we serve as an art gallery, craft store, and training center focused on traditional Mithila art forms.
          </p>
          <p class="text-lg text-gray-700 relative">
            <span class="absolute -left-4 top-0 h-full w-1 bg-gradient-to-b from-secondary-300/0 via-secondary-300 to-secondary-300/0"></span>
            Our mission is to support local artists, provide authentic Mithila artwork to art enthusiasts worldwide, and ensure this unique cultural tradition continues to thrive for generations to come.
          </p>
        </div>

        <!-- CTA Button -->
        <div class="pt-4" @scaleIn>
          <a routerLink="/about" class="group relative inline-flex items-center px-6 py-3 sm:px-8 sm:py-4 overflow-hidden rounded-full bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 text-sm sm:text-base">
            <!-- Button Shine Animation -->
            <span class="absolute inset-0 w-full h-full bg-gradient-to-r from-white/0 via-white/20 to-white/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></span>

            <span class="relative z-10 flex items-center">
              <span>Learn More About Us</span>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </span>
          </a>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>


<!-- Art Categories Section with Enhanced Floating Design -->
<app-mithila-section
  primaryColor="#008C8C"
  secondaryColor="#C1440E"
  backgroundGradient="from-accent-50 via-background-light to-primary-50"
  backgroundOpacity="12"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <div class="container relative" @fadeIn>
    <!-- Section Header with Animation -->
    <app-section-title
      title="Explore Art Categories"
      subtitle="Browse our diverse collection of traditional Mithila art forms"
    ></app-section-title>

    <!-- Categories with Staggered Animation -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 lg:gap-10" @staggerIn>
      <!-- Category 1: Paintings -->
      <div class="group relative overflow-hidden rounded-xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2" @fadeSlideInLeft>
        <!-- Category Image with Gradient Border -->
        <div class="relative h-64 sm:h-80 md:h-96 overflow-hidden">
          <!-- Gradient Border Animation -->
          <div class="absolute -inset-0.5 bg-gradient-to-br from-primary-300 via-accent-300 to-primary-300 rounded-xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-border-gradient"></div>

          <div class="relative rounded-xl overflow-hidden">
            <img src="https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg"
                 alt="Paintings"
                 class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">

            <!-- Animated Decorative Border -->
            <div class="absolute inset-4 border border-white/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
              <!-- Animated Corner Elements -->
              <div class="absolute top-0 left-0 w-6 h-6 border-t-2 border-l-2 border-white/60 opacity-0 group-hover:opacity-100 transition-all duration-700 delay-100"></div>
              <div class="absolute top-0 right-0 w-6 h-6 border-t-2 border-r-2 border-white/60 opacity-0 group-hover:opacity-100 transition-all duration-700 delay-200"></div>
              <div class="absolute bottom-0 left-0 w-6 h-6 border-b-2 border-l-2 border-white/60 opacity-0 group-hover:opacity-100 transition-all duration-700 delay-300"></div>
              <div class="absolute bottom-0 right-0 w-6 h-6 border-b-2 border-r-2 border-white/60 opacity-0 group-hover:opacity-100 transition-all duration-700 delay-400"></div>
            </div>

            <!-- Enhanced Content Overlay with Gradient -->
            <div class="absolute inset-0 bg-gradient-to-t from-primary-900/90 via-primary-800/70 to-transparent flex flex-col justify-end p-8">
              <!-- Floating Particles -->
              <div class="absolute inset-0 overflow-hidden opacity-0 group-hover:opacity-30 transition-opacity duration-700">
                <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-white rounded-full animate-float-slow"></div>
                <div class="absolute top-1/3 right-1/3 w-3 h-3 bg-white rounded-full animate-float-medium"></div>
                <div class="absolute bottom-1/4 right-1/4 w-2 h-2 bg-white rounded-full animate-float-fast"></div>
              </div>

              <!-- Animated Category Icon -->
              <div class="relative bg-white/10 backdrop-blur-sm w-16 h-16 rounded-full flex items-center justify-center mb-4 transform -translate-y-4 opacity-0 group-hover:opacity-100 group-hover:translate-y-0 transition-all duration-500">
                <!-- Icon Glow Effect -->
                <div class="absolute inset-0 rounded-full bg-primary-500/20 animate-pulse-slow"></div>

                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>

              <!-- Enhanced Category Title with Gradient Text -->
              <h3 class="text-3xl font-bold text-white mb-3 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-secondary-300 group-hover:to-white group-hover:bg-clip-text transition-all duration-500">Paintings</h3>

              <!-- Enhanced Category Description with Animation -->
              <p class="text-white/80 mb-6 max-w-xs transform translate-y-2 opacity-90 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500 delay-100">Traditional Mithila paintings on paper, canvas, and fabric with intricate details and vibrant colors</p>

              <!-- Enhanced Explore Link with Animation -->
              <a routerLink="/products"
                 class="group relative inline-flex items-center overflow-hidden rounded-full bg-white/10 backdrop-blur-sm px-4 py-2 text-white font-medium transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500 delay-200 hover:bg-white/20">
                <!-- Button Shine Animation -->
                <span class="absolute inset-0 w-full h-full bg-gradient-to-r from-white/0 via-white/20 to-white/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></span>

                <span class="relative z-10 flex items-center">
                  <span>Explore Paintings</span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Category 2: Clay Crafts -->
      <div class="group relative overflow-hidden rounded-xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2" @fadeSlideIn>
        <!-- Category Image -->
        <div class="relative h-96 overflow-hidden">
          <img src="https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg"
               alt="Clay Crafts"
               class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">

          <!-- Decorative Border -->
          <div class="absolute inset-4 border border-white/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

          <!-- Content Overlay -->
          <div class="absolute inset-0 bg-gradient-to-t from-secondary-900/90 via-secondary-800/70 to-transparent flex flex-col justify-end p-8">
            <!-- Category Icon -->
            <div class="bg-white/10 backdrop-blur-sm w-16 h-16 rounded-full flex items-center justify-center mb-4 transform -translate-y-4 opacity-0 group-hover:opacity-100 group-hover:translate-y-0 transition-all duration-500">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
            </div>

            <!-- Category Title -->
            <h3 class="text-3xl font-bold text-white mb-3 group-hover:text-primary-300 transition-colors duration-300">Clay Crafts</h3>

            <!-- Category Description -->
            <p class="text-white/80 mb-6 max-w-xs">Handcrafted clay items with traditional Mithila designs, perfect for home decor and cultural displays</p>

            <!-- Explore Link -->
            <a routerLink="/products"
               class="inline-flex items-center text-white font-medium group-hover:text-primary-300 transition-colors duration-300">
              <span>Explore Clay Crafts</span>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transform group-hover:translate-x-2 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </a>
          </div>
        </div>
      </div>

      <!-- Category 3: Textiles -->
      <div class="group relative overflow-hidden rounded-xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2" @fadeSlideInRight>
        <!-- Category Image -->
        <div class="relative h-96 overflow-hidden">
          <img src="https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg"
               alt="Textiles"
               class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">

          <!-- Decorative Border -->
          <div class="absolute inset-4 border border-white/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

          <!-- Content Overlay -->
          <div class="absolute inset-0 bg-gradient-to-t from-accent-900/90 via-accent-800/70 to-transparent flex flex-col justify-end p-8">
            <!-- Category Icon -->
            <div class="bg-white/10 backdrop-blur-sm w-16 h-16 rounded-full flex items-center justify-center mb-4 transform -translate-y-4 opacity-0 group-hover:opacity-100 group-hover:translate-y-0 transition-all duration-500">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
              </svg>
            </div>

            <!-- Category Title -->
            <h3 class="text-3xl font-bold text-white mb-3 group-hover:text-secondary-300 transition-colors duration-300">Textiles</h3>

            <!-- Category Description -->
            <p class="text-white/80 mb-6 max-w-xs">Sarees, suits, and fabrics adorned with Mithila motifs, blending traditional art with contemporary fashion</p>

            <!-- Explore Link -->
            <a routerLink="/products"
               class="inline-flex items-center text-white font-medium group-hover:text-secondary-300 transition-colors duration-300">
              <span>Explore Textiles</span>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transform group-hover:translate-x-2 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Explore More Button -->
    <div class="text-center mt-16">
      <a routerLink="/gallery" class="inline-block px-8 py-4 bg-white text-primary-600 border-2 border-primary-600 rounded-full shadow-sm hover:bg-primary-50 transition-colors duration-300 transform hover:-translate-y-1 hover:shadow-md">
        <span class="flex items-center">
          <span>Explore More Categories</span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
          </svg>
        </span>
      </a>
    </div>
  </div>
</app-mithila-section>

<!-- Featured Artists Section - Unique to Home Page -->
<app-mithila-section
  primaryColor="#264653"
  secondaryColor="#3B945E"
  backgroundGradient="from-accent-50 via-background-light to-primary-50"
  backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Meet Our Master Artists"
    subtitle="Discover the talented individuals preserving Mithila art traditions"
  ></app-section-title>

  <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
    <!-- Featured Artist 1 -->
    <div class="mithila-floating-card group">
      <div class="relative overflow-hidden h-64">
        <img src="https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg" alt="Sarita Devi" class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-70 group-hover:opacity-90 transition-opacity duration-300"></div>
        <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
          <h3 class="text-xl font-bold mb-2">Sarita Devi</h3>
          <p class="text-white/80 text-sm">Master Artist & Founder</p>
        </div>
      </div>
      <div class="p-6">
        <p class="text-gray-700 mb-4 text-sm">With over 20 years of experience, Sarita specializes in traditional Mithila painting techniques passed down through generations.</p>
        <a routerLink="/artists/1" class="text-primary-600 hover:text-primary-700 font-medium text-sm">View Profile →</a>
      </div>
    </div>

    <!-- Featured Artist 2 -->
    <div class="mithila-floating-card group">
      <div class="relative overflow-hidden h-64">
        <img src="https://i.etsystatic.com/43638819/r/il/9b1cc7/5978434663/il_794xN.5978434663_hk13.jpg" alt="Ramesh Kumar" class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-70 group-hover:opacity-90 transition-opacity duration-300"></div>
        <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
          <h3 class="text-xl font-bold mb-2">Ramesh Kumar</h3>
          <p class="text-white/80 text-sm">Contemporary Artist</p>
        </div>
      </div>
      <div class="p-6">
        <p class="text-gray-700 mb-4 text-sm">Ramesh blends traditional Mithila techniques with contemporary themes, creating unique pieces that speak to modern audiences.</p>
        <a routerLink="/artists/2" class="text-primary-600 hover:text-primary-700 font-medium text-sm">View Profile →</a>
      </div>
    </div>

    <!-- Featured Artist 3 -->
    <div class="mithila-floating-card group">
      <div class="relative overflow-hidden h-64">
        <img src="https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg" alt="Anita Jha" class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-70 group-hover:opacity-90 transition-opacity duration-300"></div>
        <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
          <h3 class="text-xl font-bold mb-2">Anita Jha</h3>
          <p class="text-white/80 text-sm">Master Artist & Instructor</p>
        </div>
      </div>
      <div class="p-6">
        <p class="text-gray-700 mb-4 text-sm">Anita specializes in traditional Mithila painting techniques and leads our workshops and training programs for aspiring artists.</p>
        <a routerLink="/artists/3" class="text-primary-600 hover:text-primary-700 font-medium text-sm">View Profile →</a>
      </div>
    </div>
  </div>

  <div class="text-center mt-12">
    <a routerLink="/artists" class="inline-block px-8 py-4 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-300 transform hover:-translate-y-1 hover:shadow-lg">
      <span class="flex items-center">
        <span>Meet All Our Artists</span>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
        </svg>
      </span>
    </a>
  </div>
</app-mithila-section>

<!-- Upcoming Events Section with Enhanced Floating Design -->
<app-mithila-section
  primaryColor="#3B945E"
  secondaryColor="#C1440E"
  backgroundGradient="from-success-50 via-background-light to-primary-50"
  backgroundOpacity="12"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <div class="container relative" @fadeIn>
    <!-- Section Header with Animation -->
    <app-section-title
      title="Upcoming Events"
      subtitle="Join us for workshops, exhibitions, and cultural events"
    ></app-section-title>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8" @staggerIn>
      <div *ngFor="let event of upcomingEvents" class="card overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
        <div class="relative overflow-hidden">
          <img [src]="event.imageUrl" [alt]="event.title" class="w-full h-48 object-cover transition-transform duration-500 hover:scale-110">
          <div class="absolute top-0 right-0 bg-primary-500 text-white px-3 py-1 m-2 rounded-full text-sm font-semibold">
            {{event.date.split(',')[0]}}
          </div>
        </div>
        <div class="p-6">
          <h3 class="text-xl font-semibold mb-2 text-gray-900 hover:text-primary-600 transition-colors duration-300">{{event.title}}</h3>
          <div class="flex items-center text-gray-600 mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-primary-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span>{{event.date}}</span>
          </div>
          <div class="flex items-center text-gray-600 mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-primary-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{{event.time}}</span>
          </div>
          <div class="flex items-center text-gray-600 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-primary-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span>{{event.location}}</span>
          </div>
          <p class="text-gray-700 mb-4">{{event.description}}</p>
          <a routerLink="/events" [queryParams]="{id: event.id}" class="btn btn-outline hover:bg-primary-50 transition-colors duration-300">Learn More</a>
        </div>
      </div>
    </div>

    <div class="text-center mt-12" @scaleIn>
      <a routerLink="/events" class="btn btn-primary hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">View All Events</a>
    </div>
  </div>
</app-mithila-section>

<!-- CTA Section with Enhanced Floating Design -->
<app-mithila-section
  primaryColor="#C1440E"
  secondaryColor="#F4B400"
  backgroundGradient="from-primary-500 via-primary-600 to-primary-700"
  backgroundOpacity="20"
  padding="py-16 sm:py-20 md:py-24"
  classes="bg-primary-500"
  [showDecorativeElements]="true">

  <div class="text-center text-white" @fadeIn>
    <app-section-title
      title="Experience the Beauty of Mithila Art"
      subtitle="Visit our gallery in Janakpur or shop online to bring home a piece of this rich cultural heritage"
      classes="text-white"
    ></app-section-title>

    <div class="flex flex-col sm:flex-row justify-center gap-4 mt-8" @staggerIn>
      <a routerLink="/contact" class="btn bg-white text-primary-600 hover:bg-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
        <span class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          Visit Our Gallery
        </span>
      </a>
      <a routerLink="/products" class="btn bg-secondary-500 text-white hover:bg-secondary-600 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
        <span class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
          </svg>
          Shop Online
        </span>
      </a>
    </div>
  </div>
</app-mithila-section>
