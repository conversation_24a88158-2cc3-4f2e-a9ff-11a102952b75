<!-- Hero Section -->
<div class="relative h-[90vh] sm:h-screen max-h-[800px] overflow-hidden" @fadeIn>
  <!-- Multi-layered Background with Parallax Effect -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat transform transition-transform duration-10000 hover:scale-105"
       style="background-image: url('https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg');">
  </div>

  <!-- Animated Gradient Background -->
  <div class="absolute inset-0 overflow-hidden animate-gradient-background">
    <!-- Gradient Overlay -->
    <div class="absolute inset-0 bg-gradient-to-br from-primary-600/90 via-primary-500/80 to-secondary-600/90"></div>
    <!-- Animated Gradient Accent -->
    <div class="absolute inset-0 bg-gradient-to-tr from-accent-500/30 via-secondary-500/20 to-transparent animate-gradient-shift"></div>
    <!-- Floating Elements Animation -->
    <div class="absolute inset-0 overflow-hidden opacity-30">
      <!-- Floating Circle 1 - Gradient Border -->
      <div class="absolute w-40 h-40 rounded-full top-[10%] left-[15%] animate-float-slow">
        <div class="absolute inset-0 rounded-full border-4 border-transparent bg-gradient-to-r from-secondary-300 via-secondary-500 to-accent-400 opacity-60 animate-spin-slow" style="mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); mask-composite: exclude; -webkit-mask-composite: source-out; padding: 4px;"></div>
      </div>

      <!-- Floating Circle 2 - Gradient Fill -->
      <div class="absolute w-24 h-24 rounded-full top-[30%] right-[10%] animate-float-medium overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-secondary-400/40 to-accent-300/30 backdrop-blur-sm animate-pulse-slow"></div>
      </div>

      <!-- Floating Circle 3 - Animated Gradient -->
      <div class="absolute w-32 h-32 rounded-full bottom-[15%] left-[25%] animate-float-fast overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-tr from-primary-400/30 via-secondary-300/20 to-accent-400/30 animate-gradient-rotate"></div>
      </div>

      <!-- Floating Circle 4 - Glowing Effect -->
      <div class="absolute w-16 h-16 rounded-full top-[60%] right-[20%] animate-float-slow overflow-hidden">
        <div class="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
        <div class="absolute inset-0 bg-gradient-to-r from-secondary-500/40 to-accent-400/30 animate-pulse-medium"></div>
      </div>

      <!-- Additional Floating Elements -->
      <!-- Floating Diamond -->
      <div class="absolute w-20 h-20 top-[45%] left-[10%] animate-float-medium transform rotate-45">
        <div class="absolute inset-0 bg-gradient-to-br from-primary-300/30 to-secondary-400/20 backdrop-blur-sm"></div>
      </div>

      <!-- Floating Rectangle -->
      <div class="absolute w-32 h-16 bottom-[30%] right-[15%] animate-float-slow overflow-hidden rounded-lg">
        <div class="absolute inset-0 bg-gradient-to-tr from-accent-400/30 to-primary-300/20 backdrop-blur-sm animate-pulse-slow"></div>
      </div>

      <!-- Floating Mithila Elements -->
      <div class="absolute w-20 h-20 top-[20%] left-[80%] animate-float-medium">
        <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" class="w-full h-full text-white/30">
          <path fill="currentColor" d="M50,10 C70,10 85,25 85,50 C85,75 70,90 50,90 C30,90 15,75 15,50 C15,25 30,10 50,10 Z M50,20 C35,20 25,35 25,50 C25,65 35,80 50,80 C65,80 75,65 75,50 C75,35 65,20 50,20 Z"/>
          <path fill="none" stroke="currentColor" stroke-width="2" d="M30,50 L70,50 M50,30 L50,70"/>
        </svg>
      </div>
      <div class="absolute w-16 h-16 bottom-[30%] left-[10%] animate-float-fast">
        <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" class="w-full h-full text-white/30">
          <path fill="none" stroke="currentColor" stroke-width="2" d="M20,20 L80,20 L80,80 L20,80 Z M30,30 L70,30 L70,70 L30,70 Z"/>
          <circle cx="50" cy="50" r="10" fill="currentColor"/>
        </svg>
      </div>
    </div>

    <!-- Animated Mithila Pattern Background with Gradient -->
    <div class="absolute top-0 left-0 w-full h-full opacity-20 animate-pulse-slow">
      <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="mithila-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#FFD700" stop-opacity="0.7">
              <animate attributeName="stop-opacity" values="0.7;0.3;0.7" dur="5s" repeatCount="indefinite" />
            </stop>
            <stop offset="50%" stop-color="#FFFFFF" stop-opacity="0.9">
              <animate attributeName="stop-opacity" values="0.9;0.5;0.9" dur="5s" repeatCount="indefinite" />
            </stop>
            <stop offset="100%" stop-color="#FFD700" stop-opacity="0.7">
              <animate attributeName="stop-opacity" values="0.7;0.3;0.7" dur="5s" repeatCount="indefinite" />
            </stop>
          </linearGradient>

          <pattern id="mithila-pattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
            <circle cx="30" cy="30" r="12" fill="none" stroke="url(#mithila-gradient)" stroke-width="1">
              <animate attributeName="r" values="12;14;12" dur="3s" repeatCount="indefinite" />
            </circle>
            <circle cx="30" cy="30" r="6" fill="none" stroke="url(#mithila-gradient)" stroke-width="1">
              <animate attributeName="r" values="6;8;6" dur="3s" repeatCount="indefinite" />
            </circle>
            <path d="M10,30 H20 M40,30 H50 M30,10 V20 M30,40 V50" stroke="url(#mithila-gradient)" stroke-width="1">
              <animate attributeName="stroke-width" values="1;1.5;1" dur="4s" repeatCount="indefinite" />
            </path>
            <path d="M15,15 L25,25 M35,35 L45,45 M15,45 L25,35 M35,25 L45,15" stroke="url(#mithila-gradient)" stroke-width="0.5">
              <animate attributeName="stroke-width" values="0.5;1;0.5" dur="4s" repeatCount="indefinite" />
            </path>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#mithila-pattern)">
          <animate attributeName="opacity" values="0.8;0.4;0.8" dur="8s" repeatCount="indefinite" />
        </rect>
      </svg>
    </div>

    <!-- Decorative Border with Gradient Animation -->
    <div class="absolute top-6 left-6 right-6 bottom-6 pointer-events-none overflow-hidden rounded-lg">
      <!-- Gradient Border Animation -->
      <div class="absolute inset-0 border-2 border-transparent bg-gradient-to-r from-secondary-300 via-white/70 to-accent-300 opacity-60 animate-border-gradient"
           style="mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); mask-composite: exclude; -webkit-mask-composite: source-out; padding: 2px;"></div>
    </div>

    <!-- Inner Decorative Border -->
    <div class="absolute top-10 left-10 right-10 bottom-10 border border-dashed border-white/30 pointer-events-none animate-pulse-medium rounded-lg"></div>

    <!-- Animated Corner Decorations with Gradients -->
    <!-- Top Left Corner -->
    <div class="absolute top-6 left-6 w-24 h-24 animate-corner-top-left overflow-hidden">
      <div class="absolute top-0 left-0 w-full h-full border-t-2 border-l-2 border-transparent bg-gradient-to-br from-secondary-300 to-white/70 opacity-70"
           style="mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); mask-composite: exclude; -webkit-mask-composite: source-out; padding: 2px 0 0 2px;"></div>
    </div>

    <!-- Top Right Corner -->
    <div class="absolute top-6 right-6 w-24 h-24 animate-corner-top-right overflow-hidden">
      <div class="absolute top-0 right-0 w-full h-full border-t-2 border-r-2 border-transparent bg-gradient-to-bl from-secondary-300 to-white/70 opacity-70"
           style="mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); mask-composite: exclude; -webkit-mask-composite: source-out; padding: 2px 2px 0 0;"></div>
    </div>

    <!-- Bottom Left Corner -->
    <div class="absolute bottom-6 left-6 w-24 h-24 animate-corner-bottom-left overflow-hidden">
      <div class="absolute bottom-0 left-0 w-full h-full border-b-2 border-l-2 border-transparent bg-gradient-to-tr from-secondary-300 to-white/70 opacity-70"
           style="mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); mask-composite: exclude; -webkit-mask-composite: source-out; padding: 0 0 2px 2px;"></div>
    </div>

    <!-- Bottom Right Corner -->
    <div class="absolute bottom-6 right-6 w-24 h-24 animate-corner-bottom-right overflow-hidden">
      <div class="absolute bottom-0 right-0 w-full h-full border-b-2 border-r-2 border-transparent bg-gradient-to-tl from-secondary-300 to-white/70 opacity-70"
           style="mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); mask-composite: exclude; -webkit-mask-composite: source-out; padding: 0 2px 2px 0;"></div>
    </div>
  </div>

  <!-- Hero Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <div class="max-w-5xl mx-auto">
      <!-- Animated Logo/Brand Mark -->
      <div class="mb-6 sm:mb-8 relative">
        <div class="inline-block p-3 sm:p-5 rounded-full bg-white/10 backdrop-blur-md border border-white/30 shadow-2xl animate-float-slow">
          <div class="absolute inset-0 rounded-full bg-secondary-500/20 animate-ping-slow"></div>
          <div class="relative">
            <img src="https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg"
                 alt="Mithilani Ghar Logo"
                 class="h-16 w-16 sm:h-20 sm:w-20 md:h-28 md:w-28 object-cover rounded-full border-2 border-secondary-500 shadow-lg transform transition-all duration-500 hover:scale-110 hover:shadow-secondary-500/50">
            <!-- Decorative Circles Around Logo -->
            <div class="absolute -inset-2 sm:-inset-4 border-2 border-dashed border-white/20 rounded-full animate-spin-slow"></div>
            <div class="absolute -inset-4 sm:-inset-8 border border-white/10 rounded-full animate-reverse-spin-slow"></div>
          </div>
        </div>
      </div>

      <!-- Animated Title with Letter Animation -->
      <h1 class="text-5xl md:text-7xl font-bold text-white mb-4 font-display tracking-wide overflow-hidden">
        <span class="block">
          <span class="inline-block animate-letter-fade-in-1">M</span>
          <span class="inline-block animate-letter-fade-in-2">i</span>
          <span class="inline-block animate-letter-fade-in-3">t</span>
          <span class="inline-block animate-letter-fade-in-4">h</span>
          <span class="inline-block animate-letter-fade-in-5">i</span>
          <span class="inline-block animate-letter-fade-in-6">l</span>
          <span class="inline-block animate-letter-fade-in-7">a</span>
          <span class="inline-block animate-letter-fade-in-8">n</span>
          <span class="inline-block animate-letter-fade-in-9">i</span>
          <span class="inline-block animate-letter-fade-in-10">&nbsp;</span>
          <span class="inline-block animate-letter-fade-in-11">G</span>
          <span class="inline-block animate-letter-fade-in-12">h</span>
          <span class="inline-block animate-letter-fade-in-13">a</span>
          <span class="inline-block animate-letter-fade-in-14">r</span>
        </span>
      </h1>

      <!-- Animated Decorative Divider -->
      <div class="relative h-2 mx-auto mb-8 w-0 animate-width-expand overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-secondary-300 via-secondary-500 to-secondary-300 rounded-full shadow-lg"></div>
        <div class="absolute inset-0 bg-white/30 animate-shimmer"></div>
      </div>

      <!-- Rotating Words Animation with Enhanced Styling -->
      <div class="flex justify-center items-center h-16 mb-6 overflow-hidden">
        <div class="text-2xl md:text-3xl text-white font-serif">
          <span class="opacity-0 animate-fade-in-delay-1">Discover the World of </span>
          <span class="relative inline-block">
            <span class="text-secondary-300 font-bold animate-word-highlight">{{currentWord}}</span>
            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-secondary-500 animate-underline-expand"></span>
          </span>
        </div>
      </div>

      <!-- Enhanced Typewriter Effect with Glowing Text -->
      <div class="relative h-12 mb-10 overflow-hidden">
        <p class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed font-serif overflow-hidden whitespace-nowrap animate-text-glow" @textTyping>
          Preserving and Promoting the Rich Artistic Heritage of Mithila
        </p>
      </div>

      <!-- Quick Links with Enhanced Animation -->
      <div class="flex flex-wrap justify-center gap-3 sm:gap-4 md:gap-5 mb-8 sm:mb-12" @staggerIn>

        <!-- Gallery Button -->
        <a routerLink="/gallery"
           class="group relative px-4 py-3 sm:px-5 sm:py-3 md:px-7 md:py-4 bg-white/10 backdrop-blur-md hover:bg-secondary-500/80 text-white rounded-lg transition-all duration-500 transform hover:-translate-y-2 hover:shadow-xl flex items-center border border-white/20 overflow-hidden text-sm sm:text-base">
          <!-- Button Background Animation -->
          <div class="absolute inset-0 bg-gradient-to-r from-secondary-500/0 via-secondary-500/30 to-secondary-500/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
          <!-- Icon with Animation -->
          <div class="relative z-10 mr-3 group-hover:scale-110 transition-transform duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <!-- Text -->
          <span class="relative z-10 font-medium">Explore Gallery</span>
        </a>

        <!-- Events Button -->
        <a routerLink="/events"
           class="group relative px-4 py-3 sm:px-5 sm:py-3 md:px-7 md:py-4 bg-white/10 backdrop-blur-md hover:bg-secondary-500/80 text-white rounded-lg transition-all duration-500 transform hover:-translate-y-2 hover:shadow-xl flex items-center border border-white/20 overflow-hidden text-sm sm:text-base">
          <!-- Button Background Animation -->
          <div class="absolute inset-0 bg-gradient-to-r from-secondary-500/0 via-secondary-500/30 to-secondary-500/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
          <!-- Icon with Animation -->
          <div class="relative z-10 mr-3 group-hover:scale-110 transition-transform duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <!-- Text -->
          <span class="relative z-10 font-medium">Upcoming Events</span>
        </a>

        <!-- Contact Button -->
        <a routerLink="/contact"
           class="group relative px-4 py-3 sm:px-5 sm:py-3 md:px-7 md:py-4 bg-white/10 backdrop-blur-md hover:bg-secondary-500/80 text-white rounded-lg transition-all duration-500 transform hover:-translate-y-2 hover:shadow-xl flex items-center border border-white/20 overflow-hidden text-sm sm:text-base">
          <!-- Button Background Animation -->
          <div class="absolute inset-0 bg-gradient-to-r from-secondary-500/0 via-secondary-500/30 to-secondary-500/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
          <!-- Icon with Animation -->
          <div class="relative z-10 mr-3 group-hover:scale-110 transition-transform duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <!-- Text -->
          <span class="relative z-10 font-medium">Contact Us</span>
        </a>
      </div>

      <!-- Enhanced Scroll Down Indicator with Animation -->
      <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2">
        <div class="flex flex-col items-center">
          <span class="text-white/80 text-sm mb-2 font-serif animate-pulse-medium">Scroll Down</span>
          <div class="relative">
            <div class="absolute inset-0 bg-white/20 rounded-full animate-ping-slow"></div>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white animate-bounce-slow" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- About Section -->
<section class="section relative overflow-hidden py-16 sm:py-20 md:py-24">
  <!-- Animated Background with Gradient -->
  <div class="absolute inset-0 bg-background-light overflow-hidden">
    <!-- Gradient Background -->
    <div class="absolute inset-0 bg-gradient-to-bl from-primary-50 via-background-light to-secondary-50 animate-gradient-background opacity-70"></div>

    <!-- Mithila Pattern Background -->
    <div class="absolute inset-0 opacity-5">
      <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="about-pattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
            <circle cx="30" cy="30" r="12" fill="none" stroke="#C1440E" stroke-width="0.5"/>
            <circle cx="30" cy="30" r="6" fill="none" stroke="#C1440E" stroke-width="0.5"/>
            <path d="M10,30 H20 M40,30 H50 M30,10 V20 M30,40 V50" stroke="#C1440E" stroke-width="0.5"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#about-pattern)" />
      </svg>
    </div>

    <!-- Floating Elements -->
    <div class="absolute top-20 right-20 w-40 h-40 rounded-full border border-primary-200 opacity-40 animate-float-slow"></div>
    <div class="absolute bottom-40 left-10 w-24 h-24 rounded-full border border-secondary-200 opacity-30 animate-float-medium"></div>
    <div class="absolute top-1/2 right-1/4 w-16 h-16 bg-primary-100 rounded-full opacity-20 animate-pulse-slow"></div>
  </div>

  <!-- Decorative Border -->
  <div class="absolute top-8 left-8 right-8 bottom-8 border border-primary-100 rounded-lg pointer-events-none opacity-50"></div>

  <div class="container relative" @fadeIn>
    <!-- Section Header with Animation -->
    <div class="text-center mb-10 sm:mb-12 md:mb-16" @fadeSlideIn>
      <div class="relative mb-3 sm:mb-4">
        <div class="inline-block relative">
          <span class="inline-block px-4 py-1 sm:px-6 sm:py-2 bg-gradient-to-r from-primary-500/80 to-primary-600/80 text-white rounded-full text-sm sm:text-base font-bold tracking-wide shadow-md transform hover:scale-105 transition-transform duration-300">
            Our Story
          </span>
          <!-- Animated Glow Effect -->
          <div class="absolute -inset-1 bg-gradient-to-r from-primary-300/0 via-primary-300/40 to-primary-300/0 rounded-full blur-md animate-pulse-slow -z-10"></div>
          <!-- Decorative Elements -->
          <div class="absolute -top-1 -left-1 w-3 h-3 bg-primary-300 rounded-full animate-ping-slow"></div>
          <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-primary-300 rounded-full animate-ping-slow delay-500"></div>
        </div>
      </div>

      <h2 class="text-4xl md:text-5xl font-heading font-bold text-gray-900 mb-4 relative inline-block">
        Welcome to Mithilani Ghar
        <div class="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-primary-300 via-secondary-400 to-primary-300 rounded-full animate-shimmer overflow-hidden"></div>
      </h2>

      <p class="text-xl text-gray-600 max-w-3xl mx-auto mt-6 opacity-0 animate-fade-in-delay-1">
        A sanctuary of Mithila art and culture in the heart of Janakpur
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 items-center">
      <!-- Left Column: Image with Decorative Border -->
      <div @scaleIn class="relative">
        <!-- Main Image -->
        <div class="rounded-lg overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-rotate-1 hover:scale-105 relative z-10">
          <!-- Gradient Border -->
          <div class="absolute -inset-0.5 bg-gradient-to-br from-primary-300 via-secondary-300 to-accent-300 rounded-lg blur-sm animate-border-gradient"></div>

          <div class="relative rounded-lg overflow-hidden">
            <img src="https://i.etsystatic.com/43638819/r/il/9b1cc7/5978434663/il_794xN.5978434663_hk13.jpg" alt="Mithilani Ghar" class="w-full h-auto transition-transform duration-700 hover:scale-105">

            <!-- Overlay Gradient -->
            <div class="absolute inset-0 bg-gradient-to-t from-primary-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
        </div>

        <!-- Decorative Elements -->
        <div class="absolute -bottom-6 -right-6 w-full h-full border-4 border-dashed border-primary-200 rounded-lg z-0 animate-pulse-slow"></div>

        <!-- Floating Badge -->
        <div class="absolute -top-5 -right-5 bg-white shadow-lg rounded-full p-4 z-20 transform rotate-12 hover:rotate-0 transition-transform duration-300 animate-float-medium">
          <div class="absolute inset-0 rounded-full bg-gradient-to-br from-secondary-100 to-secondary-200 animate-spin-slow"></div>
          <img src="https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg" alt="Mithila Art" class="h-16 w-16 rounded-full object-cover relative z-10">
        </div>
      </div>

      <!-- Right Column: Content -->
      <div class="space-y-6">
        <!-- Feature Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-6 sm:mb-8" @staggerIn>
          <!-- Card 1: Art Gallery -->
          <div class="group bg-white p-3 sm:p-4 md:p-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border-l-4 border-primary-500 transform hover:-translate-y-1 hover:border-l-6 relative overflow-hidden">
            <!-- Card Background Animation -->
            <div class="absolute inset-0 bg-gradient-to-r from-primary-50/0 via-primary-50/50 to-primary-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

            <div class="flex items-start relative z-10">
              <div class="bg-primary-100 p-3 rounded-full mr-4 group-hover:bg-primary-200 transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h3 class="font-bold text-gray-900 mb-1 group-hover:text-primary-600 transition-colors duration-300">Art Gallery</h3>
                <p class="text-gray-600 text-sm">Showcasing authentic Mithila paintings and crafts</p>
              </div>
            </div>
          </div>

          <!-- Card 2: Craft Store -->
          <div class="group bg-white p-3 sm:p-4 md:p-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border-l-4 border-secondary-500 transform hover:-translate-y-1 hover:border-l-6 relative overflow-hidden">
            <!-- Card Background Animation -->
            <div class="absolute inset-0 bg-gradient-to-r from-secondary-50/0 via-secondary-50/50 to-secondary-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

            <div class="flex items-start relative z-10">
              <div class="bg-secondary-100 p-3 rounded-full mr-4 group-hover:bg-secondary-200 transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-secondary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <div>
                <h3 class="font-bold text-gray-900 mb-1 group-hover:text-secondary-600 transition-colors duration-300">Craft Store</h3>
                <p class="text-gray-600 text-sm">Handmade products by skilled local artisans</p>
              </div>
            </div>
          </div>

          <!-- Card 3: Training Center -->
          <div class="group bg-white p-3 sm:p-4 md:p-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border-l-4 border-accent-500 transform hover:-translate-y-1 hover:border-l-6 relative overflow-hidden">
            <!-- Card Background Animation -->
            <div class="absolute inset-0 bg-gradient-to-r from-accent-50/0 via-accent-50/50 to-accent-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

            <div class="flex items-start relative z-10">
              <div class="bg-accent-100 p-3 rounded-full mr-4 group-hover:bg-accent-200 transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-accent-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M12 14l9-5-9-5-9 5 9 5z" />
                  <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                </svg>
              </div>
              <div>
                <h3 class="font-bold text-gray-900 mb-1 group-hover:text-accent-600 transition-colors duration-300">Training Center</h3>
                <p class="text-gray-600 text-sm">Learn traditional Mithila art techniques</p>
              </div>
            </div>
          </div>

          <!-- Card 4: Cultural Hub -->
          <div class="group bg-white p-3 sm:p-4 md:p-5 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border-l-4 border-mithila-red transform hover:-translate-y-1 hover:border-l-6 relative overflow-hidden">
            <!-- Card Background Animation -->
            <div class="absolute inset-0 bg-gradient-to-r from-red-50/0 via-red-50/50 to-red-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

            <div class="flex items-start relative z-10">
              <div class="bg-red-100 p-3 rounded-full mr-4 group-hover:bg-red-200 transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-mithila-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 class="font-bold text-gray-900 mb-1 group-hover:text-mithila-red transition-colors duration-300">Cultural Hub</h3>
                <p class="text-gray-600 text-sm">Celebrating the heritage of Mithila region</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Main Content -->
        <div class="space-y-4" @fadeSlideIn>
          <p class="text-lg text-gray-700 relative">
            <span class="absolute -left-4 top-0 h-full w-1 bg-gradient-to-b from-primary-300/0 via-primary-300 to-primary-300/0"></span>
            Mithilani Ghar is a dedicated hub for promoting and preserving the rich artistic heritage of the Mithila region. Located in Janakpur, Nepal, we serve as an art gallery, craft store, and training center focused on traditional Mithila art forms.
          </p>
          <p class="text-lg text-gray-700 relative">
            <span class="absolute -left-4 top-0 h-full w-1 bg-gradient-to-b from-secondary-300/0 via-secondary-300 to-secondary-300/0"></span>
            Our mission is to support local artists, provide authentic Mithila artwork to art enthusiasts worldwide, and ensure this unique cultural tradition continues to thrive for generations to come.
          </p>
        </div>

        <!-- CTA Button -->
        <div class="pt-4" @scaleIn>
          <a routerLink="/about" class="group relative inline-flex items-center px-6 py-3 sm:px-8 sm:py-4 overflow-hidden rounded-full bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 text-sm sm:text-base">
            <!-- Button Shine Animation -->
            <span class="absolute inset-0 w-full h-full bg-gradient-to-r from-white/0 via-white/20 to-white/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></span>

            <span class="relative z-10 flex items-center">
              <span>Learn More About Us</span>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </span>
          </a>
        </div>
      </div>
    </div>
  </div>
</section>


<!-- Art Categories Section -->
<section class="section relative overflow-hidden py-16 sm:py-20 md:py-24">
  <!-- Animated Background with Gradient -->
  <div class="absolute inset-0 overflow-hidden">
    <!-- Gradient Background -->
    <div class="absolute inset-0 bg-gradient-to-tl from-accent-50 via-background-light to-primary-50 animate-gradient-background opacity-70"></div>

    <!-- Mithila Pattern Background -->
    <div class="absolute inset-0 opacity-5">
      <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="category-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#008C8C" stop-opacity="0.3">
              <animate attributeName="stop-opacity" values="0.3;0.1;0.3" dur="4s" repeatCount="indefinite" />
            </stop>
            <stop offset="100%" stop-color="#C1440E" stop-opacity="0.3">
              <animate attributeName="stop-opacity" values="0.3;0.1;0.3" dur="4s" repeatCount="indefinite" />
            </stop>
          </linearGradient>
          <pattern id="category-pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
            <circle cx="20" cy="20" r="1.5" fill="url(#category-gradient)"/>
            <circle cx="0" cy="0" r="1.5" fill="url(#category-gradient)"/>
            <circle cx="0" cy="40" r="1.5" fill="url(#category-gradient)"/>
            <circle cx="40" cy="0" r="1.5" fill="url(#category-gradient)"/>
            <circle cx="40" cy="40" r="1.5" fill="url(#category-gradient)"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#category-pattern)" />
      </svg>
    </div>

    <!-- Floating Elements -->
    <div class="absolute top-40 right-20 w-32 h-32 rounded-full border border-accent-200 opacity-40 animate-float-slow"></div>
    <div class="absolute bottom-20 left-10 w-24 h-24 rounded-full border border-primary-200 opacity-30 animate-float-medium"></div>
    <div class="absolute top-1/3 left-1/3 w-16 h-16 bg-accent-100 rounded-full opacity-20 animate-pulse-slow"></div>
  </div>

  <!-- Decorative Border -->
  <div class="absolute top-8 left-8 right-8 bottom-8 border border-accent-100 rounded-lg pointer-events-none opacity-50"></div>

  <div class="container relative" @fadeIn>
    <!-- Section Header with Animation -->
    <div class="text-center mb-10 sm:mb-12 md:mb-16" @fadeSlideIn>
      <div class="relative mb-3 sm:mb-4">
        <div class="inline-block relative">
          <span class="inline-block px-4 py-1 sm:px-6 sm:py-2 bg-gradient-to-r from-accent-500/80 to-accent-600/80 text-white rounded-full text-sm sm:text-base font-bold tracking-wide shadow-md transform hover:scale-105 transition-transform duration-300">
            Art Forms
          </span>
          <!-- Animated Glow Effect -->
          <div class="absolute -inset-1 bg-gradient-to-r from-accent-300/0 via-accent-300/40 to-accent-300/0 rounded-full blur-md animate-pulse-slow -z-10"></div>
          <!-- Decorative Elements -->
          <div class="absolute -top-1 -left-1 w-3 h-3 bg-accent-300 rounded-full animate-ping-slow"></div>
          <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-accent-300 rounded-full animate-ping-slow delay-500"></div>
        </div>
      </div>

      <h2 class="text-4xl md:text-5xl font-heading font-bold text-gray-900 mb-4 relative inline-block">
        Explore Art Categories
        <div class="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-accent-300 via-primary-400 to-accent-300 rounded-full animate-shimmer overflow-hidden"></div>
      </h2>

      <p class="text-xl text-gray-600 max-w-3xl mx-auto mt-6 opacity-0 animate-fade-in-delay-1">
        Browse our diverse collection of traditional Mithila art forms
      </p>
    </div>

    <!-- Categories with Staggered Animation -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 lg:gap-10" @staggerIn>
      <!-- Category 1: Paintings -->
      <div class="group relative overflow-hidden rounded-xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2" @fadeSlideInLeft>
        <!-- Category Image with Gradient Border -->
        <div class="relative h-64 sm:h-80 md:h-96 overflow-hidden">
          <!-- Gradient Border Animation -->
          <div class="absolute -inset-0.5 bg-gradient-to-br from-primary-300 via-accent-300 to-primary-300 rounded-xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-border-gradient"></div>

          <div class="relative rounded-xl overflow-hidden">
            <img src="https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg"
                 alt="Paintings"
                 class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">

            <!-- Animated Decorative Border -->
            <div class="absolute inset-4 border border-white/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
              <!-- Animated Corner Elements -->
              <div class="absolute top-0 left-0 w-6 h-6 border-t-2 border-l-2 border-white/60 opacity-0 group-hover:opacity-100 transition-all duration-700 delay-100"></div>
              <div class="absolute top-0 right-0 w-6 h-6 border-t-2 border-r-2 border-white/60 opacity-0 group-hover:opacity-100 transition-all duration-700 delay-200"></div>
              <div class="absolute bottom-0 left-0 w-6 h-6 border-b-2 border-l-2 border-white/60 opacity-0 group-hover:opacity-100 transition-all duration-700 delay-300"></div>
              <div class="absolute bottom-0 right-0 w-6 h-6 border-b-2 border-r-2 border-white/60 opacity-0 group-hover:opacity-100 transition-all duration-700 delay-400"></div>
            </div>

            <!-- Enhanced Content Overlay with Gradient -->
            <div class="absolute inset-0 bg-gradient-to-t from-primary-900/90 via-primary-800/70 to-transparent flex flex-col justify-end p-8">
              <!-- Floating Particles -->
              <div class="absolute inset-0 overflow-hidden opacity-0 group-hover:opacity-30 transition-opacity duration-700">
                <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-white rounded-full animate-float-slow"></div>
                <div class="absolute top-1/3 right-1/3 w-3 h-3 bg-white rounded-full animate-float-medium"></div>
                <div class="absolute bottom-1/4 right-1/4 w-2 h-2 bg-white rounded-full animate-float-fast"></div>
              </div>

              <!-- Animated Category Icon -->
              <div class="relative bg-white/10 backdrop-blur-sm w-16 h-16 rounded-full flex items-center justify-center mb-4 transform -translate-y-4 opacity-0 group-hover:opacity-100 group-hover:translate-y-0 transition-all duration-500">
                <!-- Icon Glow Effect -->
                <div class="absolute inset-0 rounded-full bg-primary-500/20 animate-pulse-slow"></div>

                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>

              <!-- Enhanced Category Title with Gradient Text -->
              <h3 class="text-3xl font-bold text-white mb-3 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-secondary-300 group-hover:to-white group-hover:bg-clip-text transition-all duration-500">Paintings</h3>

              <!-- Enhanced Category Description with Animation -->
              <p class="text-white/80 mb-6 max-w-xs transform translate-y-2 opacity-90 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500 delay-100">Traditional Mithila paintings on paper, canvas, and fabric with intricate details and vibrant colors</p>

              <!-- Enhanced Explore Link with Animation -->
              <a routerLink="/shop" [queryParams]="{category: 'paintings'}"
                 class="group relative inline-flex items-center overflow-hidden rounded-full bg-white/10 backdrop-blur-sm px-4 py-2 text-white font-medium transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500 delay-200 hover:bg-white/20">
                <!-- Button Shine Animation -->
                <span class="absolute inset-0 w-full h-full bg-gradient-to-r from-white/0 via-white/20 to-white/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></span>

                <span class="relative z-10 flex items-center">
                  <span>Explore Paintings</span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Category 2: Clay Crafts -->
      <div class="group relative overflow-hidden rounded-xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2" @fadeSlideIn>
        <!-- Category Image -->
        <div class="relative h-96 overflow-hidden">
          <img src="https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg"
               alt="Clay Crafts"
               class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">

          <!-- Decorative Border -->
          <div class="absolute inset-4 border border-white/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

          <!-- Content Overlay -->
          <div class="absolute inset-0 bg-gradient-to-t from-secondary-900/90 via-secondary-800/70 to-transparent flex flex-col justify-end p-8">
            <!-- Category Icon -->
            <div class="bg-white/10 backdrop-blur-sm w-16 h-16 rounded-full flex items-center justify-center mb-4 transform -translate-y-4 opacity-0 group-hover:opacity-100 group-hover:translate-y-0 transition-all duration-500">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
            </div>

            <!-- Category Title -->
            <h3 class="text-3xl font-bold text-white mb-3 group-hover:text-primary-300 transition-colors duration-300">Clay Crafts</h3>

            <!-- Category Description -->
            <p class="text-white/80 mb-6 max-w-xs">Handcrafted clay items with traditional Mithila designs, perfect for home decor and cultural displays</p>

            <!-- Explore Link -->
            <a routerLink="/shop" [queryParams]="{category: 'clay-crafts'}"
               class="inline-flex items-center text-white font-medium group-hover:text-primary-300 transition-colors duration-300">
              <span>Explore Clay Crafts</span>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transform group-hover:translate-x-2 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </a>
          </div>
        </div>
      </div>

      <!-- Category 3: Textiles -->
      <div class="group relative overflow-hidden rounded-xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2" @fadeSlideInRight>
        <!-- Category Image -->
        <div class="relative h-96 overflow-hidden">
          <img src="https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg"
               alt="Textiles"
               class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">

          <!-- Decorative Border -->
          <div class="absolute inset-4 border border-white/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

          <!-- Content Overlay -->
          <div class="absolute inset-0 bg-gradient-to-t from-accent-900/90 via-accent-800/70 to-transparent flex flex-col justify-end p-8">
            <!-- Category Icon -->
            <div class="bg-white/10 backdrop-blur-sm w-16 h-16 rounded-full flex items-center justify-center mb-4 transform -translate-y-4 opacity-0 group-hover:opacity-100 group-hover:translate-y-0 transition-all duration-500">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
              </svg>
            </div>

            <!-- Category Title -->
            <h3 class="text-3xl font-bold text-white mb-3 group-hover:text-secondary-300 transition-colors duration-300">Textiles</h3>

            <!-- Category Description -->
            <p class="text-white/80 mb-6 max-w-xs">Sarees, suits, and fabrics adorned with Mithila motifs, blending traditional art with contemporary fashion</p>

            <!-- Explore Link -->
            <a routerLink="/shop" [queryParams]="{category: 'textiles'}"
               class="inline-flex items-center text-white font-medium group-hover:text-secondary-300 transition-colors duration-300">
              <span>Explore Textiles</span>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transform group-hover:translate-x-2 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Explore More Button -->
    <div class="text-center mt-16">
      <a routerLink="/gallery" class="inline-block px-8 py-4 bg-white text-primary-600 border-2 border-primary-600 rounded-full shadow-sm hover:bg-primary-50 transition-colors duration-300 transform hover:-translate-y-1 hover:shadow-md">
        <span class="flex items-center">
          <span>Explore More Categories</span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
          </svg>
        </span>
      </a>
    </div>
  </div>
</section>

<!-- Testimonials Section -->
<section class="section bg-gray-50 py-24 relative overflow-hidden">
  <!-- Decorative Elements -->
  <div class="absolute top-0 left-0 w-full h-20 bg-gradient-to-b from-white to-transparent"></div>
  <div class="absolute bottom-0 left-0 w-full h-20 bg-gradient-to-t from-white to-transparent"></div>

  <!-- Decorative Quotes -->
  <div class="absolute top-12 left-12 opacity-5">
    <svg class="h-48 w-48 text-primary-500" fill="currentColor" viewBox="0 0 24 24">
      <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
    </svg>
  </div>
  <div class="absolute bottom-12 right-12 opacity-5 transform rotate-180">
    <svg class="h-48 w-48 text-primary-500" fill="currentColor" viewBox="0 0 24 24">
      <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
    </svg>
  </div>

  <div class="container relative" @fadeIn>
    <!-- Section Header with Animation -->
    <div class="text-center mb-16" @fadeSlideIn>
      <div class="relative mb-4">
        <div class="inline-block relative">
          <span class="inline-block px-6 py-2 bg-gradient-to-r from-primary-500/80 to-primary-700/80 text-white rounded-full text-base font-bold tracking-wide shadow-md transform hover:scale-105 transition-transform duration-300">
            Testimonials
          </span>
          <!-- Animated Glow Effect -->
          <div class="absolute -inset-1 bg-gradient-to-r from-primary-300/0 via-primary-300/40 to-primary-300/0 rounded-full blur-md animate-pulse-slow -z-10"></div>
          <!-- Decorative Elements -->
          <div class="absolute -top-1 -left-1 w-3 h-3 bg-primary-300 rounded-full animate-ping-slow"></div>
          <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-primary-300 rounded-full animate-ping-slow delay-500"></div>
        </div>
      </div>

      <h2 class="text-4xl md:text-5xl font-heading font-bold text-gray-900 mb-4 relative inline-block">
        What Our Visitors Say
        <div class="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-primary-300 via-primary-500 to-primary-300 rounded-full animate-shimmer overflow-hidden"></div>
      </h2>

      <p class="text-xl text-gray-600 max-w-3xl mx-auto mt-6 opacity-0 animate-fade-in-delay-1">
        Hear from art enthusiasts who have experienced the magic of Mithilani Ghar
      </p>
    </div>

    <!-- Testimonial Cards -->
    <div class="max-w-5xl mx-auto" @staggerIn>
      <!-- Testimonial 1 - Featured Testimonial -->
      <div class="bg-white rounded-xl shadow-xl overflow-hidden mb-12 transform transition-all duration-500 hover:shadow-2xl hover:-translate-y-1">
        <div class="grid grid-cols-1 md:grid-cols-3">
          <!-- Image Column -->
          <div class="relative h-64 md:h-auto">
            <img [src]="testimonials[0].image" [alt]="testimonials[0].name" class="absolute inset-0 w-full h-full object-cover">
            <div class="absolute inset-0 bg-gradient-to-r from-primary-500/80 to-primary-700/80 mix-blend-multiply"></div>
            <div class="absolute bottom-0 left-0 p-6 text-white">
              <h4 class="font-bold text-xl">{{testimonials[0].name}}</h4>
              <p class="text-white/80">{{testimonials[0].role}}</p>
            </div>
          </div>

          <!-- Content Column -->
          <div class="p-8 md:col-span-2 flex flex-col justify-center">
            <div class="mb-6">
              <svg class="h-10 w-10 text-primary-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
              </svg>
            </div>
            <p class="text-gray-700 text-lg italic leading-relaxed mb-6">"{{testimonials[0].quote}}"</p>
            <div class="flex items-center">
              <div class="flex">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </div>
              <span class="ml-2 text-gray-600">Exceptional Experience</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Other Testimonials -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Testimonial 2 -->
        <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 relative">
          <!-- Quote Icon -->
          <div class="absolute top-4 right-4 text-primary-100">
            <svg class="h-16 w-16" fill="currentColor" viewBox="0 0 24 24">
              <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
            </svg>
          </div>

          <!-- Content -->
          <div class="relative z-10">
            <p class="text-gray-700 italic mb-6 leading-relaxed">"{{testimonials[1].quote}}"</p>

            <!-- Author -->
            <div class="flex items-center">
              <img [src]="testimonials[1].image" [alt]="testimonials[1].name" class="w-14 h-14 rounded-full object-cover border-2 border-primary-500 p-0.5">
              <div class="ml-4">
                <h4 class="font-semibold text-gray-900">{{testimonials[1].name}}</h4>
                <p class="text-sm text-gray-600">{{testimonials[1].role}}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Testimonial 3 -->
        <div class="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 relative">
          <!-- Quote Icon -->
          <div class="absolute top-4 right-4 text-primary-100">
            <svg class="h-16 w-16" fill="currentColor" viewBox="0 0 24 24">
              <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
            </svg>
          </div>

          <!-- Content -->
          <div class="relative z-10">
            <p class="text-gray-700 italic mb-6 leading-relaxed">"{{testimonials[2].quote}}"</p>

            <!-- Author -->
            <div class="flex items-center">
              <img [src]="testimonials[2].image" [alt]="testimonials[2].name" class="w-14 h-14 rounded-full object-cover border-2 border-primary-500 p-0.5">
              <div class="ml-4">
                <h4 class="font-semibold text-gray-900">{{testimonials[2].name}}</h4>
                <p class="text-sm text-gray-600">{{testimonials[2].role}}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- View More Testimonials Button -->
      <div class="text-center mt-12">
        <a href="#" class="inline-block px-6 py-3 bg-white text-primary-600 border border-primary-600 rounded-full hover:bg-primary-50 transition-colors duration-300 shadow-sm hover:shadow">
          <span class="flex items-center">
            <span>View More Testimonials</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </span>
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Upcoming Events Section -->
<section class="section relative overflow-hidden py-24">
  <!-- Animated Background with Gradient -->
  <div class="absolute inset-0 overflow-hidden">
    <!-- Gradient Background -->
    <div class="absolute inset-0 bg-gradient-to-bl from-success-50 via-background-light to-primary-50 animate-gradient-background opacity-70"></div>

    <!-- Mithila Pattern Background -->
    <div class="absolute inset-0 opacity-5">
      <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="events-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#3B945E" stop-opacity="0.3">
              <animate attributeName="stop-opacity" values="0.3;0.1;0.3" dur="4s" repeatCount="indefinite" />
            </stop>
            <stop offset="100%" stop-color="#C1440E" stop-opacity="0.3">
              <animate attributeName="stop-opacity" values="0.3;0.1;0.3" dur="4s" repeatCount="indefinite" />
            </stop>
          </linearGradient>
          <pattern id="events-pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
            <path d="M0,20 H40 M20,0 V40" stroke="url(#events-gradient)" stroke-width="0.5"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#events-pattern)" />
      </svg>
    </div>

    <!-- Floating Elements -->
    <div class="absolute top-40 left-20 w-32 h-32 rounded-full border border-success-200 opacity-40 animate-float-slow"></div>
    <div class="absolute bottom-20 right-10 w-24 h-24 rounded-full border border-primary-200 opacity-30 animate-float-medium"></div>
    <div class="absolute top-1/3 right-1/3 w-16 h-16 bg-success-100 rounded-full opacity-20 animate-pulse-slow"></div>
  </div>

  <!-- Decorative Border -->
  <div class="absolute top-8 left-8 right-8 bottom-8 border border-success-100 rounded-lg pointer-events-none opacity-50"></div>

  <div class="container relative" @fadeIn>
    <!-- Section Header with Animation -->
    <div class="text-center mb-16" @fadeSlideIn>
      <div class="relative mb-4">
        <div class="inline-block relative">
          <span class="inline-block px-6 py-2 bg-gradient-to-r from-success-500/80 to-success-600/80 text-white rounded-full text-base font-bold tracking-wide shadow-md transform hover:scale-105 transition-transform duration-300">
            Events
          </span>
          <!-- Animated Glow Effect -->
          <div class="absolute -inset-1 bg-gradient-to-r from-success-300/0 via-success-300/40 to-success-300/0 rounded-full blur-md animate-pulse-slow -z-10"></div>
          <!-- Decorative Elements -->
          <div class="absolute -top-1 -left-1 w-3 h-3 bg-success-300 rounded-full animate-ping-slow"></div>
          <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-success-300 rounded-full animate-ping-slow delay-500"></div>
        </div>
      </div>

      <h2 class="text-4xl md:text-5xl font-heading font-bold text-gray-900 mb-4 relative inline-block">
        Upcoming Events
        <div class="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-success-300 via-success-500 to-success-300 rounded-full animate-shimmer overflow-hidden"></div>
      </h2>

      <p class="text-xl text-gray-600 max-w-3xl mx-auto mt-6 opacity-0 animate-fade-in-delay-1">
        Join us for workshops, exhibitions, and cultural events
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8" @staggerIn>
      <div *ngFor="let event of upcomingEvents" class="card overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
        <div class="relative overflow-hidden">
          <img [src]="event.imageUrl" [alt]="event.title" class="w-full h-48 object-cover transition-transform duration-500 hover:scale-110">
          <div class="absolute top-0 right-0 bg-primary-500 text-white px-3 py-1 m-2 rounded-full text-sm font-semibold">
            {{event.date.split(',')[0]}}
          </div>
        </div>
        <div class="p-6">
          <h3 class="text-xl font-semibold mb-2 text-gray-900 hover:text-primary-600 transition-colors duration-300">{{event.title}}</h3>
          <div class="flex items-center text-gray-600 mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-primary-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span>{{event.date}}</span>
          </div>
          <div class="flex items-center text-gray-600 mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-primary-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{{event.time}}</span>
          </div>
          <div class="flex items-center text-gray-600 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-primary-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span>{{event.location}}</span>
          </div>
          <p class="text-gray-700 mb-4">{{event.description}}</p>
          <a routerLink="/events" [queryParams]="{id: event.id}" class="btn btn-outline hover:bg-primary-50 transition-colors duration-300">Learn More</a>
        </div>
      </div>
    </div>

    <div class="text-center mt-12" @scaleIn>
      <a routerLink="/events" class="btn btn-primary hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">View All Events</a>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-16 bg-primary-500 relative overflow-hidden">
  <!-- Decorative Mithila Pattern Background -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute top-0 left-0 w-64 h-64 border-8 border-white rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
    <div class="absolute bottom-0 right-0 w-96 h-96 border-8 border-white rounded-full transform translate-x-1/3 translate-y-1/3"></div>
    <div class="absolute top-1/2 left-1/2 w-48 h-48 border-4 border-white rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
  </div>

  <div class="container relative z-10">
    <div class="text-center" @fadeIn>
      <!-- Section Label -->
      <div class="relative mb-6 inline-block">
        <div class="inline-block relative">
          <span class="inline-block px-6 py-2 bg-white/20 backdrop-blur-sm text-white rounded-full text-base font-bold tracking-wide shadow-md transform hover:scale-105 transition-transform duration-300 border border-white/30">
            Experience Mithila
          </span>
          <!-- Animated Glow Effect -->
          <div class="absolute -inset-1 bg-gradient-to-r from-white/0 via-white/30 to-white/0 rounded-full blur-md animate-pulse-slow -z-10"></div>
          <!-- Decorative Elements -->
          <div class="absolute -top-1 -left-1 w-3 h-3 bg-white/50 rounded-full animate-ping-slow"></div>
          <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-white/50 rounded-full animate-ping-slow delay-500"></div>
        </div>
      </div>

      <h2 class="text-3xl md:text-4xl font-bold text-white mb-6 font-heading relative inline-block">
        Experience the Beauty of Mithila Art
        <div class="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-white/50 via-white/80 to-white/50 rounded-full animate-shimmer overflow-hidden"></div>
      </h2>

      <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
        Visit our gallery in Janakpur or shop online to bring home a piece of this rich cultural heritage.
      </p>
      <div class="flex flex-col sm:flex-row justify-center gap-4" @staggerIn>
        <a routerLink="/contact" class="btn bg-white text-primary-600 hover:bg-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">Visit Us</a>
      </div>
    </div>
  </div>
</section>
