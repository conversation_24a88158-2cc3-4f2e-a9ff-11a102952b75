import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CartService, CartItem } from '../../services/cart.service';

export interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  category: string;
  price: number;
  images: string[];
  artist: string;
  dimensions: string;
  materials: string[];
  featured: boolean;
  detailedDescription?: string;
  artistBio?: string;
  culturalSignificance?: string;
}

@Component({
  selector: 'app-product-detail',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule],
  templateUrl: './product-detail.component.html',
  styleUrls: ['./product-detail.component.css']
})
export class ProductDetailComponent implements OnInit {
  product: Product | null = null;
  selectedImageIndex = 0;
  quantity = 1;
  showCartModal = false;
  cartItemCount = 0;

  // Sample products data (in a real app, this would come from a service)
  private products: Product[] = [
    {
      id: '1',
      name: 'Traditional Mithila Painting - Madhubani Art',
      slug: 'traditional-mithila-painting-madhubani-art',
      description: 'Authentic hand-painted Mithila artwork featuring traditional motifs and vibrant colors on handmade paper.',
      detailedDescription: 'This exquisite Mithila painting represents the rich cultural heritage of the Madhubani region. Created using traditional techniques passed down through generations, this artwork features intricate geometric patterns, floral motifs, and symbolic representations that hold deep cultural significance. The painting is executed on handmade paper using natural pigments derived from plants and minerals, ensuring authenticity and longevity.',
      category: 'Paintings',
      price: 2500,
      images: [
        'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
        'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg'
      ],
      artist: 'Sita Devi',
      artistBio: 'Sita Devi is a renowned Mithila artist with over 25 years of experience. She learned the art form from her grandmother and has been instrumental in preserving traditional techniques while adding her own contemporary touch.',
      dimensions: '16" x 12"',
      materials: ['Handmade Paper', 'Natural Pigments', 'Bamboo Brush'],
      culturalSignificance: 'Mithila paintings traditionally adorned the walls of homes during festivals and special occasions. The motifs often represent fertility, prosperity, and divine blessings.',
      featured: true
    },
    {
      id: '2',
      name: 'Decorative Clay Pot with Mithila Motifs',
      slug: 'decorative-clay-pot-mithila-motifs',
      description: 'Handcrafted clay pot adorned with traditional Mithila designs, perfect for home decoration.',
      detailedDescription: 'This beautiful clay pot showcases the traditional pottery skills of Mithila artisans. Hand-molded from local clay and decorated with authentic Mithila motifs, this piece serves both functional and decorative purposes. The intricate patterns are painted using natural colors and represent various elements of nature and spirituality.',
      category: 'Clay Crafts',
      price: 1200,
      images: [
        'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
        'https://i.etsystatic.com/43638819/r/il/9b1cc7/5978434663/il_794xN.5978434663_hk13.jpg'
      ],
      artist: 'Ram Babu Yadav',
      artistBio: 'Ram Babu Yadav comes from a family of traditional potters. He has been creating clay artifacts for over 20 years and is known for his attention to detail and authentic designs.',
      dimensions: '8" height x 6" diameter',
      materials: ['Natural Clay', 'Natural Colors', 'Traditional Tools'],
      culturalSignificance: 'Clay pots in Mithila culture are considered sacred and are used in various religious ceremonies and festivals.',
      featured: false
    },
    {
      id: '3',
      name: 'Mithila Art Saree',
      slug: 'mithila-art-saree',
      description: 'Beautiful cotton saree with hand-painted Mithila designs, blending tradition with contemporary fashion.',
      detailedDescription: 'This stunning saree combines the elegance of traditional Indian attire with the artistic beauty of Mithila paintings. Each saree is hand-painted by skilled artists, making every piece unique. The designs feature traditional motifs that tell stories of love, nature, and spirituality.',
      category: 'Textiles',
      price: 4500,
      images: [
        'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg'
      ],
      artist: 'Kamala Devi',
      artistBio: 'Kamala Devi is a master textile artist who specializes in hand-painted sarees. She has been practicing this art for over 30 years and has trained many young women in the craft.',
      dimensions: '6 yards',
      materials: ['Pure Cotton', 'Natural Dyes', 'Hand Painting'],
      culturalSignificance: 'Hand-painted sarees are worn during special occasions and festivals, representing the wearer\'s connection to cultural heritage.',
      featured: true
    }
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private cartService: CartService
  ) {}

  ngOnInit() {
    this.route.params.subscribe(params => {
      const productSlug = params['slug'];
      this.product = this.products.find(p => p.slug === productSlug) || null;

      if (!this.product) {
        this.router.navigate(['/products']);
      }
    });

    // Subscribe to cart changes
    this.cartService.cart$.subscribe(cartItems => {
      this.cartItemCount = this.cartService.getCartItemCount();
    });
  }

  selectImage(index: number) {
    this.selectedImageIndex = index;
  }

  increaseQuantity() {
    this.quantity++;
  }

  decreaseQuantity() {
    if (this.quantity > 1) {
      this.quantity--;
    }
  }

  addToCart() {
    if (this.product) {
      const cartItem: Omit<CartItem, 'quantity'> = {
        id: this.product.id,
        name: this.product.name,
        slug: this.product.slug,
        price: this.product.price,
        image: this.product.images[0],
        artist: this.product.artist
      };

      this.cartService.addToCart(cartItem, this.quantity);
      this.showCartModal = true;

      // Hide modal after 3 seconds
      setTimeout(() => {
        this.showCartModal = false;
      }, 3000);
    }
  }

  viewCart() {
    this.router.navigate(['/cart']);
  }

  formatPrice(price: number): string {
    return `NPR ${price.toLocaleString()}`;
  }

  getTotalPrice(): number {
    if (!this.product) return 0;
    return this.product.price * this.quantity;
  }

  isInCart(): boolean {
    return this.product ? this.cartService.isInCart(this.product.id) : false;
  }

  getCartQuantity(): number {
    return this.product ? this.cartService.getItemQuantity(this.product.id) : 0;
  }
}
