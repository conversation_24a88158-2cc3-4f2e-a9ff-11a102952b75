import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';

export interface Product {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  images: string[];
  artist: string;
  dimensions: string;
  materials: string[];
  featured: boolean;
  inStock: boolean;
  detailedDescription?: string;
  artistBio?: string;
  culturalSignificance?: string;
}

@Component({
  selector: 'app-product-detail',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule],
  templateUrl: './product-detail.component.html',
  styleUrls: ['./product-detail.component.css']
})
export class ProductDetailComponent implements OnInit {
  product: Product | null = null;
  selectedImageIndex = 0;
  orderForm: FormGroup;
  showOrderForm = false;
  orderSubmitted = false;

  // Sample products data (in a real app, this would come from a service)
  private products: Product[] = [
    {
      id: '1',
      name: 'Traditional Mithila Painting - Madhubani Art',
      description: 'Authentic hand-painted Mithila artwork featuring traditional motifs and vibrant colors on handmade paper.',
      detailedDescription: 'This exquisite Mithila painting represents the rich cultural heritage of the Madhubani region. Created using traditional techniques passed down through generations, this artwork features intricate geometric patterns, floral motifs, and symbolic representations that hold deep cultural significance. The painting is executed on handmade paper using natural pigments derived from plants and minerals, ensuring authenticity and longevity.',
      category: 'Paintings',
      price: 2500,
      images: [
        'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
        'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg'
      ],
      artist: 'Sita Devi',
      artistBio: 'Sita Devi is a renowned Mithila artist with over 25 years of experience. She learned the art form from her grandmother and has been instrumental in preserving traditional techniques while adding her own contemporary touch.',
      dimensions: '16" x 12"',
      materials: ['Handmade Paper', 'Natural Pigments', 'Bamboo Brush'],
      culturalSignificance: 'Mithila paintings traditionally adorned the walls of homes during festivals and special occasions. The motifs often represent fertility, prosperity, and divine blessings.',
      featured: true,
      inStock: true
    },
    {
      id: '2',
      name: 'Decorative Clay Pot with Mithila Motifs',
      description: 'Handcrafted clay pot adorned with traditional Mithila designs, perfect for home decoration.',
      detailedDescription: 'This beautiful clay pot showcases the traditional pottery skills of Mithila artisans. Hand-molded from local clay and decorated with authentic Mithila motifs, this piece serves both functional and decorative purposes. The intricate patterns are painted using natural colors and represent various elements of nature and spirituality.',
      category: 'Clay Crafts',
      price: 1200,
      images: [
        'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
        'https://i.etsystatic.com/43638819/r/il/9b1cc7/5978434663/il_794xN.5978434663_hk13.jpg'
      ],
      artist: 'Ram Babu Yadav',
      artistBio: 'Ram Babu Yadav comes from a family of traditional potters. He has been creating clay artifacts for over 20 years and is known for his attention to detail and authentic designs.',
      dimensions: '8" height x 6" diameter',
      materials: ['Natural Clay', 'Natural Colors', 'Traditional Tools'],
      culturalSignificance: 'Clay pots in Mithila culture are considered sacred and are used in various religious ceremonies and festivals.',
      featured: false,
      inStock: true
    },
    {
      id: '3',
      name: 'Mithila Art Saree',
      description: 'Beautiful cotton saree with hand-painted Mithila designs, blending tradition with contemporary fashion.',
      detailedDescription: 'This stunning saree combines the elegance of traditional Indian attire with the artistic beauty of Mithila paintings. Each saree is hand-painted by skilled artists, making every piece unique. The designs feature traditional motifs that tell stories of love, nature, and spirituality.',
      category: 'Textiles',
      price: 4500,
      images: [
        'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg'
      ],
      artist: 'Kamala Devi',
      artistBio: 'Kamala Devi is a master textile artist who specializes in hand-painted sarees. She has been practicing this art for over 30 years and has trained many young women in the craft.',
      dimensions: '6 yards',
      materials: ['Pure Cotton', 'Natural Dyes', 'Hand Painting'],
      culturalSignificance: 'Hand-painted sarees are worn during special occasions and festivals, representing the wearer\'s connection to cultural heritage.',
      featured: true,
      inStock: true
    }
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder
  ) {
    this.orderForm = this.fb.group({
      customerName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
      address: ['', [Validators.required, Validators.minLength(10)]],
      city: ['', [Validators.required]],
      postalCode: ['', [Validators.required]],
      country: ['', [Validators.required]],
      quantity: [1, [Validators.required, Validators.min(1)]],
      specialRequests: [''],
      agreeToTerms: [false, [Validators.requiredTrue]]
    });
  }

  ngOnInit() {
    this.route.params.subscribe(params => {
      const productId = params['id'];
      this.product = this.products.find(p => p.id === productId) || null;
      
      if (!this.product) {
        this.router.navigate(['/products']);
      }
    });
  }

  selectImage(index: number) {
    this.selectedImageIndex = index;
  }

  toggleOrderForm() {
    this.showOrderForm = !this.showOrderForm;
    if (this.showOrderForm) {
      // Scroll to form
      setTimeout(() => {
        const formElement = document.getElementById('order-form');
        if (formElement) {
          formElement.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    }
  }

  onSubmitOrder() {
    if (this.orderForm.valid && this.product) {
      // In a real application, you would send this data to your backend
      console.log('Order submitted:', {
        product: this.product,
        orderDetails: this.orderForm.value
      });
      
      this.orderSubmitted = true;
      this.showOrderForm = false;
      
      // Scroll to success message
      setTimeout(() => {
        const successElement = document.getElementById('order-success');
        if (successElement) {
          successElement.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.orderForm.controls).forEach(key => {
        this.orderForm.get(key)?.markAsTouched();
      });
    }
  }

  formatPrice(price: number): string {
    return `NPR ${price.toLocaleString()}`;
  }

  getTotalPrice(): number {
    if (!this.product) return 0;
    const quantity = this.orderForm.get('quantity')?.value || 1;
    return this.product.price * quantity;
  }

  getFieldError(fieldName: string): string {
    const field = this.orderForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['email']) return 'Please enter a valid email';
      if (field.errors['pattern']) return 'Please enter a valid phone number';
      if (field.errors['minlength']) return `${fieldName} is too short`;
      if (field.errors['min']) return 'Quantity must be at least 1';
    }
    return '';
  }
}
