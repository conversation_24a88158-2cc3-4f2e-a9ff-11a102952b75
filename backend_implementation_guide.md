# Mithilani Ghar Backend Implementation Guide (.NET)

This guide provides detailed implementation instructions for key features of the Mithilani Ghar backend.

## Project Setup

### 1. Create ASP.NET Core Web API Project

```bash
dotnet new webapi -n MithilaniGhar.API
```

### 2. Project Structure

```
MithilaniGhar/
├── MithilaniGhar.API/           # API project
├── MithilaniGhar.Core/          # Core business logic
├── MithilaniGhar.Infrastructure/ # Data access and external services
├── MithilaniGhar.Shared/        # Shared DTOs and utilities
└── MithilaniGhar.Tests/         # Unit and integration tests
```

### 3. Add Required NuGet Packages

```bash
# API Project
dotnet add package Microsoft.AspNetCore.Authentication.JwtBearer
dotnet add package Microsoft.AspNetCore.Identity.EntityFrameworkCore
dotnet add package Swashbuckle.AspNetCore
dotnet add package AutoMapper.Extensions.Microsoft.DependencyInjection
dotnet add package FluentValidation.AspNetCore

# Core Project
dotnet add package MediatR.Extensions.Microsoft.DependencyInjection

# Infrastructure Project
dotnet add package Microsoft.EntityFrameworkCore.SqlServer
dotnet add package Microsoft.EntityFrameworkCore.Tools
dotnet add package Azure.Storage.Blobs
```

## Authentication Implementation

### JWT Authentication Setup

```csharp
// Program.cs
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = builder.Configuration["Jwt:Issuer"],
        ValidAudience = builder.Configuration["Jwt:Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"]))
    };
});
```

### JWT Token Service

```csharp
public interface ITokenService
{
    string GenerateAccessToken(User user);
    string GenerateRefreshToken();
    ClaimsPrincipal GetPrincipalFromExpiredToken(string token);
}

public class TokenService : ITokenService
{
    private readonly IConfiguration _configuration;
    
    public TokenService(IConfiguration configuration)
    {
        _configuration = configuration;
    }
    
    public string GenerateAccessToken(User user)
    {
        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new Claim(ClaimTypes.Name, user.Username),
            new Claim(ClaimTypes.Email, user.Email),
            new Claim(ClaimTypes.Role, user.Role)
        };
        
        var key = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]));
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
        
        var token = new JwtSecurityToken(
            issuer: _configuration["Jwt:Issuer"],
            audience: _configuration["Jwt:Audience"],
            claims: claims,
            expires: DateTime.Now.AddMinutes(30),
            signingCredentials: creds);
            
        return new JwtSecurityTokenHandler().WriteToken(token);
    }
    
    // Implementation of other methods...
}
```

## File Storage Implementation

### Azure Blob Storage Service

```csharp
public interface IFileStorageService
{
    Task<string> UploadFileAsync(Stream fileStream, string fileName, string contentType);
    Task DeleteFileAsync(string fileUrl);
}

public class AzureBlobStorageService : IFileStorageService
{
    private readonly BlobServiceClient _blobServiceClient;
    private readonly string _containerName;
    
    public AzureBlobStorageService(IConfiguration configuration)
    {
        _blobServiceClient = new BlobServiceClient(
            configuration["AzureStorage:ConnectionString"]);
        _containerName = configuration["AzureStorage:ContainerName"];
    }
    
    public async Task<string> UploadFileAsync(Stream fileStream, string fileName, string contentType)
    {
        var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
        await containerClient.CreateIfNotExistsAsync();
        
        // Generate unique file name
        var uniqueFileName = $"{Guid.NewGuid()}_{fileName}";
        var blobClient = containerClient.GetBlobClient(uniqueFileName);
        
        // Upload file
        await blobClient.UploadAsync(fileStream, new BlobHttpHeaders { ContentType = contentType });
        
        return blobClient.Uri.ToString();
    }
    
    public async Task DeleteFileAsync(string fileUrl)
    {
        var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
        var uri = new Uri(fileUrl);
        var fileName = Path.GetFileName(uri.LocalPath);
        var blobClient = containerClient.GetBlobClient(fileName);
        
        await blobClient.DeleteIfExistsAsync();
    }
}
```

## Pagination Implementation

### Pagination Helper Classes

```csharp
public class PaginationParams
{
    private const int MaxPageSize = 50;
    private int _pageSize = 10;
    
    public int PageNumber { get; set; } = 1;
    
    public int PageSize
    {
        get => _pageSize;
        set => _pageSize = (value > MaxPageSize) ? MaxPageSize : value;
    }
}

public class PagedList<T>
{
    public int CurrentPage { get; private set; }
    public int TotalPages { get; private set; }
    public int PageSize { get; private set; }
    public int TotalCount { get; private set; }
    public bool HasPrevious => CurrentPage > 1;
    public bool HasNext => CurrentPage < TotalPages;
    public List<T> Items { get; private set; }
    
    public PagedList(List<T> items, int count, int pageNumber, int pageSize)
    {
        TotalCount = count;
        PageSize = pageSize;
        CurrentPage = pageNumber;
        TotalPages = (int)Math.Ceiling(count / (double)pageSize);
        Items = items;
    }
    
    public static async Task<PagedList<T>> CreateAsync(
        IQueryable<T> source, int pageNumber, int pageSize)
    {
        var count = await source.CountAsync();
        var items = await source.Skip((pageNumber - 1) * pageSize)
                                .Take(pageSize)
                                .ToListAsync();
                                
        return new PagedList<T>(items, count, pageNumber, pageSize);
    }
}
```

### Pagination Extension for HTTP Response

```csharp
public static class HttpExtensions
{
    public static void AddPaginationHeader<T>(
        this HttpResponse response, PagedList<T> pagedList)
    {
        var paginationHeader = new
        {
            currentPage = pagedList.CurrentPage,
            itemsPerPage = pagedList.PageSize,
            totalItems = pagedList.TotalCount,
            totalPages = pagedList.TotalPages,
            hasNext = pagedList.HasNext,
            hasPrevious = pagedList.HasPrevious
        };
        
        response.Headers.Add("X-Pagination", 
            JsonSerializer.Serialize(paginationHeader));
        response.Headers.Add("Access-Control-Expose-Headers", "X-Pagination");
    }
}
```

## Error Handling

### Global Exception Middleware

```csharp
public class ExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExceptionMiddleware> _logger;
    private readonly IHostEnvironment _env;
    
    public ExceptionMiddleware(
        RequestDelegate next,
        ILogger<ExceptionMiddleware> logger,
        IHostEnvironment env)
    {
        _next = next;
        _logger = logger;
        _env = env;
    }
    
    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            context.Response.ContentType = "application/json";
            context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            
            var response = _env.IsDevelopment()
                ? new ApiException(context.Response.StatusCode, ex.Message, ex.StackTrace?.ToString())
                : new ApiException(context.Response.StatusCode, "Internal Server Error");
                
            var options = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
            var json = JsonSerializer.Serialize(response, options);
            
            await context.Response.WriteAsync(json);
        }
    }
}

public class ApiException
{
    public int StatusCode { get; set; }
    public string Message { get; set; }
    public string Details { get; set; }
    
    public ApiException(int statusCode, string message, string details = null)
    {
        StatusCode = statusCode;
        Message = message;
        Details = details;
    }
}
```

## Validation

### FluentValidation Implementation

```csharp
public class ArtworkCreateDtoValidator : AbstractValidator<ArtworkCreateDto>
{
    public ArtworkCreateDtoValidator()
    {
        RuleFor(x => x.Title)
            .NotEmpty().WithMessage("Title is required")
            .MaximumLength(100).WithMessage("Title cannot exceed 100 characters");
            
        RuleFor(x => x.Description)
            .NotEmpty().WithMessage("Description is required")
            .MaximumLength(1000).WithMessage("Description cannot exceed 1000 characters");
            
        RuleFor(x => x.CategoryId)
            .NotEmpty().WithMessage("Category is required");
            
        RuleFor(x => x.Price)
            .GreaterThanOrEqualTo(0).WithMessage("Price must be greater than or equal to 0");
            
        RuleFor(x => x.Year)
            .NotEmpty().WithMessage("Year is required")
            .InclusiveBetween(1900, DateTime.Now.Year).WithMessage($"Year must be between 1900 and {DateTime.Now.Year}");
            
        RuleFor(x => x.Medium)
            .NotEmpty().WithMessage("Medium is required")
            .MaximumLength(100).WithMessage("Medium cannot exceed 100 characters");
            
        RuleFor(x => x.Dimensions)
            .NotEmpty().WithMessage("Dimensions are required")
            .MaximumLength(50).WithMessage("Dimensions cannot exceed 50 characters");
    }
}
```

## Caching Implementation

### Redis Cache Service

```csharp
public interface ICacheService
{
    Task<T> GetAsync<T>(string key);
    Task SetAsync<T>(string key, T value, TimeSpan? expiry = null);
    Task RemoveAsync(string key);
}

public class RedisCacheService : ICacheService
{
    private readonly IDistributedCache _cache;
    
    public RedisCacheService(IDistributedCache cache)
    {
        _cache = cache;
    }
    
    public async Task<T> GetAsync<T>(string key)
    {
        var cachedValue = await _cache.GetStringAsync(key);
        
        if (cachedValue == null)
            return default;
            
        return JsonSerializer.Deserialize<T>(cachedValue);
    }
    
    public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null)
    {
        var options = new DistributedCacheEntryOptions();
        
        if (expiry.HasValue)
            options.AbsoluteExpirationRelativeToNow = expiry;
        else
            options.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1);
            
        var serializedValue = JsonSerializer.Serialize(value);
        await _cache.SetStringAsync(key, serializedValue, options);
    }
    
    public async Task RemoveAsync(string key)
    {
        await _cache.RemoveAsync(key);
    }
}
```

## Feature-Specific Implementations

### 1. Shopping Cart Implementation

```csharp
public class CartItem
{
    public int ProductId { get; set; }
    public string ProductName { get; set; }
    public string ImageUrl { get; set; }
    public decimal Price { get; set; }
    public int Quantity { get; set; }
}

public class ShoppingCart
{
    public string UserId { get; set; }
    public List<CartItem> Items { get; set; } = new List<CartItem>();
    public decimal TotalAmount => Items.Sum(i => i.Price * i.Quantity);
}

public interface IShoppingCartService
{
    Task<ShoppingCart> GetCartAsync(string userId);
    Task<ShoppingCart> AddItemToCartAsync(string userId, CartItem item);
    Task<ShoppingCart> UpdateItemQuantityAsync(string userId, int productId, int quantity);
    Task<ShoppingCart> RemoveItemFromCartAsync(string userId, int productId);
    Task ClearCartAsync(string userId);
}

public class ShoppingCartService : IShoppingCartService
{
    private readonly ICacheService _cacheService;
    private readonly IRepository<Product> _productRepository;
    
    public ShoppingCartService(
        ICacheService cacheService,
        IRepository<Product> productRepository)
    {
        _cacheService = cacheService;
        _productRepository = productRepository;
    }
    
    private string GetCartKey(string userId) => $"cart:{userId}";
    
    // Implementation of interface methods...
}
```

### 2. Order Processing Implementation

```csharp
public class OrderProcessingService : IOrderProcessingService
{
    private readonly IRepository<Order> _orderRepository;
    private readonly IRepository<OrderItem> _orderItemRepository;
    private readonly IRepository<Product> _productRepository;
    private readonly IShoppingCartService _cartService;
    private readonly IEmailService _emailService;
    
    public OrderProcessingService(
        IRepository<Order> orderRepository,
        IRepository<OrderItem> orderItemRepository,
        IRepository<Product> productRepository,
        IShoppingCartService cartService,
        IEmailService emailService)
    {
        _orderRepository = orderRepository;
        _orderItemRepository = orderItemRepository;
        _productRepository = productRepository;
        _cartService = cartService;
        _emailService = emailService;
    }
    
    public async Task<Order> CreateOrderAsync(OrderCreateDto orderDto, string userId)
    {
        // Get user's cart
        var cart = await _cartService.GetCartAsync(userId);
        
        if (cart.Items.Count == 0)
            throw new Exception("Cannot create order with empty cart");
            
        // Check product availability
        foreach (var item in cart.Items)
        {
            var product = await _productRepository.GetByIdAsync(item.ProductId);
            
            if (product == null)
                throw new Exception($"Product with ID {item.ProductId} not found");
                
            if (product.StockQuantity < item.Quantity)
                throw new Exception($"Not enough stock for product: {product.Name}");
        }
        
        // Create order
        var order = new Order
        {
            UserId = int.Parse(userId),
            OrderNumber = GenerateOrderNumber(),
            OrderDate = DateTime.UtcNow,
            Status = "Pending",
            PaymentMethod = orderDto.PaymentMethod,
            PaymentStatus = "Pending",
            ShippingMethod = orderDto.ShippingMethod,
            ShippingCost = orderDto.ShippingCost,
            TaxAmount = orderDto.TaxAmount,
            TotalAmount = cart.TotalAmount + orderDto.ShippingCost + orderDto.TaxAmount,
            ShippingFirstName = orderDto.ShippingFirstName,
            ShippingLastName = orderDto.ShippingLastName,
            ShippingAddress = orderDto.ShippingAddress,
            ShippingCity = orderDto.ShippingCity,
            ShippingState = orderDto.ShippingState,
            ShippingCountry = orderDto.ShippingCountry,
            ShippingPostalCode = orderDto.ShippingPostalCode,
            ShippingPhone = orderDto.ShippingPhone,
            ShippingEmail = orderDto.ShippingEmail,
            Notes = orderDto.Notes
        };
        
        var createdOrder = await _orderRepository.AddAsync(order);
        
        // Create order items
        foreach (var item in cart.Items)
        {
            var orderItem = new OrderItem
            {
                OrderId = createdOrder.Id,
                ProductId = item.ProductId,
                Quantity = item.Quantity,
                UnitPrice = item.Price,
                Subtotal = item.Price * item.Quantity
            };
            
            await _orderItemRepository.AddAsync(orderItem);
            
            // Update product stock
            var product = await _productRepository.GetByIdAsync(item.ProductId);
            product.StockQuantity -= item.Quantity;
            await _productRepository.UpdateAsync(product);
        }
        
        // Clear cart
        await _cartService.ClearCartAsync(userId);
        
        // Send order confirmation email
        await _emailService.SendOrderConfirmationAsync(createdOrder);
        
        return createdOrder;
    }
    
    private string GenerateOrderNumber()
    {
        return $"ORD-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString().Substring(0, 8).ToUpper()}";
    }
    
    // Additional methods for order processing...
}
```

### 3. Blog Post Search Implementation

```csharp
public class BlogPostSearchService : IBlogPostSearchService
{
    private readonly IRepository<BlogPost> _blogPostRepository;
    private readonly ICacheService _cacheService;
    
    public BlogPostSearchService(
        IRepository<BlogPost> blogPostRepository,
        ICacheService cacheService)
    {
        _blogPostRepository = blogPostRepository;
        _cacheService = cacheService;
    }
    
    public async Task<PagedList<BlogPostDto>> SearchBlogPostsAsync(BlogPostSearchParams searchParams)
    {
        // Try to get from cache first
        var cacheKey = $"blog_search:{JsonSerializer.Serialize(searchParams)}";
        var cachedResult = await _cacheService.GetAsync<PagedList<BlogPostDto>>(cacheKey);
        
        if (cachedResult != null)
            return cachedResult;
            
        // Build query
        var query = _blogPostRepository.GetQueryable()
            .Where(p => p.IsPublished);
            
        // Apply search filters
        if (!string.IsNullOrEmpty(searchParams.Search))
        {
            query = query.Where(p => 
                p.Title.Contains(searchParams.Search) || 
                p.Content.Contains(searchParams.Search) ||
                p.Excerpt.Contains(searchParams.Search));
        }
        
        if (searchParams.CategoryId.HasValue)
        {
            query = query.Where(p => p.CategoryId == searchParams.CategoryId.Value);
        }
        
        if (searchParams.TagId.HasValue)
        {
            query = query.Where(p => p.BlogPostTags.Any(t => t.TagId == searchParams.TagId.Value));
        }
        
        if (searchParams.AuthorId.HasValue)
        {
            query = query.Where(p => p.AuthorId == searchParams.AuthorId.Value);
        }
        
        if (searchParams.Featured.HasValue)
        {
            query = query.Where(p => p.IsFeatured == searchParams.Featured.Value);
        }
        
        // Apply sorting
        query = searchParams.SortBy?.ToLower() switch
        {
            "title" => searchParams.SortDirection?.ToLower() == "desc" 
                ? query.OrderByDescending(p => p.Title) 
                : query.OrderBy(p => p.Title),
            "date" => searchParams.SortDirection?.ToLower() == "desc" 
                ? query.OrderByDescending(p => p.PublishedDate) 
                : query.OrderBy(p => p.PublishedDate),
            "views" => searchParams.SortDirection?.ToLower() == "desc" 
                ? query.OrderByDescending(p => p.ViewCount) 
                : query.OrderBy(p => p.ViewCount),
            _ => query.OrderByDescending(p => p.PublishedDate) // Default sort
        };
        
        // Create paged result
        var result = await PagedList<BlogPostDto>.CreateAsync(
            query.ProjectTo<BlogPostDto>(_mapper.ConfigurationProvider),
            searchParams.PageNumber,
            searchParams.PageSize);
            
        // Cache the result
        await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromMinutes(10));
        
        return result;
    }
}
```

## Security Considerations

### 1. Input Validation

- Use FluentValidation for all input validation
- Validate all request parameters, including query strings
- Implement model binding validation with [ApiController] attribute

### 2. SQL Injection Prevention

- Use parameterized queries with Entity Framework
- Avoid raw SQL queries where possible
- If raw SQL is necessary, use SqlParameter objects

### 3. Cross-Site Scripting (XSS) Prevention

- Encode all user-generated content when returning in responses
- Use Content-Security-Policy headers
- Implement proper HTML sanitization for rich text content

### 4. Cross-Site Request Forgery (CSRF) Protection

- Implement anti-forgery tokens for form submissions
- Use SameSite cookie attribute

### 5. Rate Limiting

```csharp
// Program.cs
builder.Services.AddRateLimiter(options =>
{
    options.GlobalLimiter = PartitionedRateLimiter.Create<HttpContext, string>(httpContext =>
        RateLimitPartition.GetFixedWindowLimiter(
            partitionKey: httpContext.Connection.RemoteIpAddress?.ToString() ?? httpContext.Request.Headers.Host.ToString(),
            factory: partition => new FixedWindowRateLimiterOptions
            {
                AutoReplenishment = true,
                PermitLimit = 100,
                QueueLimit = 0,
                Window = TimeSpan.FromMinutes(1)
            }));
            
    options.OnRejected = async (context, token) =>
    {
        context.HttpContext.Response.StatusCode = 429; // Too Many Requests
        context.HttpContext.Response.ContentType = "application/json";
        
        var response = new
        {
            title = "Too many requests",
            status = 429,
            detail = "You have exceeded the rate limit. Please try again later."
        };
        
        await context.HttpContext.Response.WriteAsJsonAsync(response, token);
    };
});
```

## Performance Optimization

### 1. Async/Await Best Practices

- Use async/await consistently throughout the application
- Avoid blocking calls
- Use ConfigureAwait(false) where appropriate

### 2. Response Compression

```csharp
// Program.cs
builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
    options.Providers.Add<BrotliCompressionProvider>();
    options.Providers.Add<GzipCompressionProvider>();
});

builder.Services.Configure<BrotliCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.Fastest;
});

builder.Services.Configure<GzipCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.Fastest;
});
```

### 3. Output Caching

```csharp
// Program.cs
builder.Services.AddOutputCache(options =>
{
    options.AddBasePolicy(builder => builder.Cache());
    
    // Custom policy for public endpoints
    options.AddPolicy("PublicEndpoint", builder =>
    {
        builder.Cache()
               .SetVaryByQuery("*")
               .Expire(TimeSpan.FromMinutes(10));
    });
});
```

## Deployment Considerations

### 1. Environment Configuration

- Use environment-specific appsettings.json files
- Store secrets in Azure Key Vault or similar service
- Use environment variables for sensitive configuration

### 2. Database Migrations

- Use EF Core migrations for database schema changes
- Implement a migration strategy for production deployments
- Consider using database seeding for initial data

### 3. Monitoring and Logging

- Implement structured logging with Serilog
- Configure Application Insights for monitoring
- Set up health checks for system components

```csharp
// Program.cs
builder.Services.AddHealthChecks()
    .AddDbContextCheck<MithilaniGharDbContext>()
    .AddCheck<RedisHealthCheck>("Redis")
    .AddCheck<BlobStorageHealthCheck>("BlobStorage");
    
app.MapHealthChecks("/health", new HealthCheckOptions
{
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});
```
