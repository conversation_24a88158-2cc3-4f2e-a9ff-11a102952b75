import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-social-proof',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './social-proof.component.html',
  styleUrl: './social-proof.component.css',
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('600ms ease-in', style({ opacity: 1 })),
      ]),
    ]),
    trigger('countUp', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('1000ms ease-out', style({ opacity: 1 })),
      ]),
    ]),
  ]
})
export class SocialProofComponent implements OnInit {
  guestCount = 0;
  targetGuestCount = 500;
  
  badges = [
    {
      name: 'TripAdvisor',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Tripadvisor_Logo_circle-green_vertical-lockup_registered_RGB.svg/1200px-Tripadvisor_Logo_circle-green_vertical-lockup_registered_RGB.svg.png',
      link: 'https://www.tripadvisor.com'
    },
    {
      name: 'Booking.com',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/b/be/Booking.com_logo.svg/2560px-Booking.com_logo.svg.png',
      link: 'https://www.booking.com'
    },
    {
      name: 'Expedia',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/5b/Expedia_2012_logo.svg/1280px-Expedia_2012_logo.svg.png',
      link: 'https://www.expedia.com'
    }
  ];
  
  ngOnInit() {
    this.animateCounter();
  }
  
  animateCounter() {
    const duration = 2000; // 2 seconds
    const interval = 50; // Update every 50ms
    const steps = duration / interval;
    const increment = this.targetGuestCount / steps;
    
    const timer = setInterval(() => {
      this.guestCount = Math.min(this.guestCount + increment, this.targetGuestCount);
      
      if (this.guestCount >= this.targetGuestCount) {
        this.guestCount = this.targetGuestCount;
        clearInterval(timer);
      } else {
        this.guestCount = Math.floor(this.guestCount);
      }
    }, interval);
  }
}
