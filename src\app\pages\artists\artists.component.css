/* Artists page specific styles */

/* Animation for floating elements */
@keyframes float-slow {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes float-medium {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-6px); }
}

@keyframes pulse-slow {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.5; }
}

@keyframes border-gradient {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 0.3; }
}

.animate-float-slow {
  animation: float-slow 6s ease-in-out infinite;
}

.animate-float-medium {
  animation: float-medium 4s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.animate-border-gradient {
  animation: border-gradient 4s ease-in-out infinite;
}

/* Fade-in animation */
@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-fade-in {
  animation: fade-in 1.5s ease-out forwards;
}

/* Artist card hover effects */
.artist-card {
  transition: all 0.3s ease;
}

.artist-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Workshop card styles */
.workshop-card {
  position: relative;
  overflow: hidden;
}

.workshop-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(193, 68, 14, 0.05), rgba(244, 180, 0, 0.05));
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  z-index: 0;
}

.workshop-card:hover::before {
  transform: translateX(0);
}

/* Line clamp for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
}

/* Spotlight section styles */
.achievement-item {
  position: relative;
  padding-left: 1.5rem;
}

.achievement-item::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #C1440E;
  font-size: 1.25rem;
  line-height: 1;
}
