import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-art-animation',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './art-animation.component.html',
  styleUrl: './art-animation.component.css'
})
export class ArtAnimationComponent implements OnInit {
  artElements: any[] = [];
  
  ngOnInit() {
    // Generate random art elements
    this.generateArtElements();
  }
  
  generateArtElements() {
    const elements = [];
    const shapes = ['circle', 'square', 'triangle', 'line', 'dot', 'fish', 'flower', 'bird', 'leaf'];
    
    // Generate 15-25 random elements
    const count = Math.floor(Math.random() * 10) + 15;
    
    for (let i = 0; i < count; i++) {
      elements.push({
        shape: shapes[Math.floor(Math.random() * shapes.length)],
        top: Math.floor(Math.random() * 100) + '%',
        left: Math.floor(Math.random() * 100) + '%',
        size: Math.floor(Math.random() * 40) + 10 + 'px',
        rotation: Math.floor(Math.random() * 360) + 'deg',
        delay: Math.random() * 5 + 's',
        duration: Math.random() * 10 + 10 + 's'
      });
    }
    
    this.artElements = elements;
  }
}
