import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeroBannerComponent } from '../../components/shared/hero-banner/hero-banner.component';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../../components/shared/mithila-border/mithila-border.component';
import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';

@Component({
  selector: 'app-about',
  standalone: true,
  imports: [
    CommonModule,
    SectionTitleComponent,
    MithilaSectionComponent,
    MithilaArtBackgroundComponent,
    MithilaBorderComponent,
    MithilaDecorativeElementComponent
  ],
  templateUrl: './about.component.html',
  styleUrl: './about.component.css'
})
export class AboutComponent {
  teamMembers = [
    {
      name: 'Sarita Devi',
      role: 'Founder & Lead Artist',
      bio: 'With over 25 years of experience in traditional Mithila art, Sarita founded Mithilani Ghar to preserve and promote this unique cultural heritage.',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg'
    },
    {
      name: 'Ramesh Kumar',
      role: 'Gallery Manager',
      bio: 'Ramesh oversees the day-to-day operations of the gallery and works closely with artists to curate exhibitions and workshops.',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg'
    },
    {
      name: 'Anita Jha',
      role: 'Master Artist & Instructor',
      bio: 'Anita specializes in traditional Mithila painting techniques and leads our workshops and training programs for aspiring artists.',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg'
    },
    {
      name: 'Sunil Yadav',
      role: 'Marketing & Outreach',
      bio: 'Sunil handles our digital presence and collaborates with cultural organizations to promote Mithila art globally.',
      image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg'
    }
  ];
}
