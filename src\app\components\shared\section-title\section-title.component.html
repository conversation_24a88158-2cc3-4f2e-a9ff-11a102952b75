<div class="mb-12 {{classes}}" [ngClass]="{
  'text-left': alignment === 'left',
  'text-center': alignment === 'center',
  'text-right': alignment === 'right'
}">
  <h2 class="text-3xl md:text-4xl font-bold mb-4" [ngClass]="classes.includes('text-white') ? 'text-white' : 'text-gray-900'">{{title}}</h2>

  <div *ngIf="decorative" class="relative">
    <div class="absolute inset-0 flex items-center" [ngClass]="{
      'justify-start': alignment === 'left',
      'justify-center': alignment === 'center',
      'justify-end': alignment === 'right'
    }">
      <div class="w-16 border-t-2" [ngClass]="classes.includes('text-white') ? 'border-white/50' : 'border-primary-500'"></div>
    </div>
  </div>

  <p *ngIf="subtitle" class="mt-6 text-lg max-w-3xl" [ngClass]="{
    'mx-auto': alignment === 'center',
    'ml-auto': alignment === 'right',
    'text-white/90': classes.includes('text-white'),
    'text-gray-600': !classes.includes('text-white')
  }">{{subtitle}}</p>
</div>
