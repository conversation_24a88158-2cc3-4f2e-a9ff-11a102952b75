<div class="mb-12" [ngClass]="{
  'text-left': alignment === 'left',
  'text-center': alignment === 'center',
  'text-right': alignment === 'right'
}">
  <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{{title}}</h2>
  
  <div *ngIf="decorative" class="relative">
    <div class="absolute inset-0 flex items-center" [ngClass]="{
      'justify-start': alignment === 'left',
      'justify-center': alignment === 'center',
      'justify-end': alignment === 'right'
    }">
      <div class="w-16 border-t-2 border-primary-500"></div>
    </div>
  </div>
  
  <p *ngIf="subtitle" class="mt-6 text-lg text-gray-600 max-w-3xl" [ngClass]="{
    'mx-auto': alignment === 'center',
    'ml-auto': alignment === 'right'
  }">{{subtitle}}</p>
</div>
