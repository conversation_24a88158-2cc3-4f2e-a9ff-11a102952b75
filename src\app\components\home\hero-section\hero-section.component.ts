import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-hero-section',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './hero-section.component.html',
  styleUrl: './hero-section.component.css',
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('1000ms ease-in', style({ opacity: 1 })),
      ]),
    ]),
    trigger('slideUp', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(20px)' }),
        animate('1200ms 200ms ease-out', style({ opacity: 1, transform: 'translateY(0)' })),
      ]),
    ]),
    trigger('pulse', [
      transition(':enter', [
        style({ opacity: 0, transform: 'scale(0.95)' }),
        animate('1500ms 500ms ease-out', style({ opacity: 1, transform: 'scale(1)' })),
      ]),
    ]),
  ]
})
export class HeroSectionComponent implements OnInit {
  currentSlide = 0;
  slides = [
    {
      image: 'https://images.unsplash.com/photo-1609609830354-8f615d61f7bc?q=80&w=1974&auto=format&fit=crop',
      alt: 'Janaki Temple in Janakpur'
    },
    {
      image: 'https://images.unsplash.com/photo-1625046438416-cf1a3b139c89?q=80&w=2070&auto=format&fit=crop',
      alt: 'Traditional Mithila Art'
    },
    {
      image: 'https://images.unsplash.com/photo-1580739824572-f2501d59f962?q=80&w=1974&auto=format&fit=crop',
      alt: 'Mithilani Ghar Exterior'
    }
  ];

  ngOnInit() {
    // Start slideshow
    this.startSlideshow();
  }

  startSlideshow() {
    setInterval(() => {
      this.currentSlide = (this.currentSlide + 1) % this.slides.length;
    }, 5000); // Change slide every 5 seconds
  }
}
