<!-- <PERSON> Banner with <PERSON><PERSON><PERSON> Elements -->
<div class="relative overflow-hidden">
  <app-hero-banner
    title="Contact Us"
    subtitle="Get in touch with us for inquiries, collaborations, or to visit our gallery"
    backgroundImage="/assets/images/contact-hero.jpg"
    height="h-80"
  ></app-hero-banner>

  <!-- Decorative Art Background -->
  <app-mithila-art-background
    [primaryColor]="'#C1440E'"
    [secondaryColor]="'#F4B400'"
    [opacity]="'0.15'"
  ></app-mithila-art-background>

  <!-- Decorative Border -->
  <app-mithila-border
    [primaryColor]="'#C1440E'"
    [secondaryColor]="'#F4B400'"
    [position]="'bottom'"
    [height]="'30px'"
  ></app-mithila-border>
</div>

<!-- Contact Section -->
<app-mithila-section
  primaryColor="#008C8C"
  secondaryColor="#D81B60"
  backgroundGradient="from-peacock-50 via-background-light to-magenta-50"
  backgroundOpacity="15"
  padding="py-16"
  [showDecorativeElements]="true"
>
  <div class="container">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
      <!-- Contact Form -->
      <div class="bg-white/90 backdrop-blur-sm rounded-lg shadow-md p-6 relative overflow-hidden group">
        <!-- Decorative Background -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary-50/0 via-primary-50/50 to-primary-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

        <app-section-title
          title="Send Us a Message"
          subtitle="Fill out the form below and we'll get back to you as soon as possible."
          alignment="left"
        ></app-section-title>

        <form (ngSubmit)="submitForm()" class="space-y-6 mt-8 relative z-10" @slideUp>
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
            <input
              type="text"
              id="name"
              name="name"
              [(ngModel)]="contactForm.name"
              required
              class="input"
              placeholder="Your full name"
            >
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
              <input
                type="email"
                id="email"
                name="email"
                [(ngModel)]="contactForm.email"
                required
                class="input"
                placeholder="Your email address"
              >
            </div>
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                [(ngModel)]="contactForm.phone"
                class="input"
                placeholder="Your phone number (optional)"
              >
            </div>
          </div>

          <div>
            <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
            <input
              type="text"
              id="subject"
              name="subject"
              [(ngModel)]="contactForm.subject"
              required
              class="input"
              placeholder="What is this regarding?"
            >
          </div>

          <div>
            <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Message</label>
            <textarea
              id="message"
              name="message"
              [(ngModel)]="contactForm.message"
              required
              rows="5"
              class="input"
              placeholder="Your message"
            ></textarea>
          </div>

          <div>
            <button type="submit" class="btn btn-primary w-full md:w-auto">Send Message</button>
          </div>
        </form>

        <!-- Decorative Element -->
        <app-mithila-decorative-element
          [primaryColor]="'#D81B60'"
          [secondaryColor]="'#008C8C'"
          [type]="'lotus'"
          position="absolute -bottom-4 -right-4"
          classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"
          size="40px">
        </app-mithila-decorative-element>
      </div>

      <!-- Contact Information -->
      <div class="bg-white/90 backdrop-blur-sm rounded-lg shadow-md p-6 relative overflow-hidden group">
        <!-- Decorative Background -->
        <div class="absolute inset-0 bg-gradient-to-br from-peacock-50/0 via-peacock-50/50 to-peacock-50/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

        <app-section-title
          title="Contact Information"
          subtitle="Visit us or reach out through any of the following channels."
          alignment="left"
        ></app-section-title>

        <div class="mt-8 space-y-8 relative z-10" @staggerIn>
          <!-- Address -->
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-100 text-primary-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Our Location</h3>
              <p class="mt-1 text-gray-600">Barahbigha, Janaki Mandir Marg, Janakpurdham-08, Dhanusha, Nepal</p>
              <p class="mt-2 text-sm text-primary-600">
                <a href="https://maps.google.com" target="_blank" class="flex items-center">
                  Get Directions
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </p>
            </div>
          </div>

          <!-- Phone -->
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-100 text-primary-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Phone Numbers</h3>
              <p class="mt-1 text-gray-600">+977-9814830580 / +977-9821762884</p>
              <p class="mt-2 text-sm text-primary-600">
                <a href="tel:+9779814830580" class="flex items-center">
                  Call Us
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </p>
            </div>
          </div>

          <!-- Email -->
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-100 text-primary-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Email</h3>
              <p class="mt-1 text-gray-600">mithilanighargmail.com</p>
              <p class="mt-2 text-sm text-primary-600">
                <a href="mailto:<EMAIL>" class="flex items-center">
                  Send Email
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </p>
            </div>
          </div>

          <!-- Hours -->
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-100 text-primary-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Opening Hours</h3>
              <p class="mt-1 text-gray-600">Open daily: 9:00 AM - 8:00 PM</p>
              <p class="mt-1 text-gray-600">We are open all days of the week including holidays.</p>
            </div>
          </div>
        </div>

        <!-- Social Media Links -->
        <div class="mt-10">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Connect With Us</h3>
          <div class="flex space-x-4">
            <a *ngFor="let social of socialLinks"
               [href]="social.url"
               target="_blank"
               [ngClass]="social.color"
               class="flex items-center justify-center h-10 w-10 rounded-full text-white transition-transform duration-300 hover:scale-110">
              <i [class]="social.icon"></i>
            </a>
          </div>
        </div>

        <!-- Decorative Element -->
        <app-mithila-decorative-element
          [primaryColor]="'#008C8C'"
          [secondaryColor]="'#D81B60'"
          [type]="'peacock'"
          position="absolute -bottom-4 -right-4"
          classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"
          size="40px">
        </app-mithila-decorative-element>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Map Section with Mithila Art Elements -->
<section class="relative overflow-hidden">
  <div class="h-96 w-full">
    <iframe
      src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3571.5668128441087!2d85.92093!3d26.7271!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39ec4144bd00f9b3%3A0x4e9fe97a37b9c128!2sJanaki%20Mandir!5e0!3m2!1sen!2sus!4v1651234567890!5m2!1sen!2sus"
      width="100%"
      height="100%"
      style="border:0;"
      allowfullscreen=""
      loading="lazy"
      referrerpolicy="no-referrer-when-downgrade">
    </iframe>
  </div>

  <!-- Decorative Border -->
  <app-mithila-border
    [primaryColor]="'#F4B400'"
    [secondaryColor]="'#C1440E'"
    [position]="'top'"
    [height]="'30px'"
  ></app-mithila-border>
</section>

<!-- FAQ Section -->
<app-mithila-section
  primaryColor="#3B945E"
  secondaryColor="#F7A700"
  backgroundGradient="from-success-50 via-background-light to-secondary-50"
  backgroundOpacity="15"
  padding="py-16"
  [showDecorativeElements]="true"
>
  <app-section-title
    title="Frequently Asked Questions"
    subtitle="Find answers to common questions about our gallery and services"
  ></app-section-title>

  <div class="max-w-3xl mx-auto mt-12">
    <div class="space-y-6" @staggerIn>
      <div *ngFor="let faq of faqItems; let i = index"
           class="bg-white/80 backdrop-blur-sm rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900">{{faq.question}}</h3>
          <p class="mt-2 text-gray-600">{{faq.answer}}</p>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Call to Action -->
<app-mithila-section
  primaryColor="#264653"
  secondaryColor="#C1440E"
  backgroundGradient="from-accent-50 via-background-light to-primary-50"
  backgroundOpacity="15"
  padding="py-16"
  [showDecorativeElements]="true"
>
  <div class="max-w-4xl mx-auto text-center">
    <h2 class="text-3xl font-bold text-gray-900 mb-4">Visit Our Gallery</h2>
    <p class="text-xl text-gray-600 mb-8">Experience the rich tradition of Mithila art in person at our gallery in Janakpurdham.</p>
    <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
      <a href="tel:+9779814830580" class="btn btn-primary">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
        </svg>
        Call Us
      </a>
      <a href="mailto:<EMAIL>" class="btn btn-secondary">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
        Email Us
      </a>
    </div>
  </div>
</app-mithila-section>
