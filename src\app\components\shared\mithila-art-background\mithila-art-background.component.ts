import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-mithila-art-background',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="absolute inset-0 overflow-hidden opacity-{{opacity}} pointer-events-none">
      <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="mithila-gradient-{{uniqueId}}" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" [attr.stop-color]="primaryColor" stop-opacity="0.7">
              <animate attributeName="stop-opacity" values="0.7;0.3;0.7" dur="5s" repeatCount="indefinite" />
            </stop>
            <stop offset="50%" stop-color="#FFFFFF" stop-opacity="0.9">
              <animate attributeName="stop-opacity" values="0.9;0.5;0.9" dur="5s" repeatCount="indefinite" />
            </stop>
            <stop offset="100%" [attr.stop-color]="secondaryColor" stop-opacity="0.7">
              <animate attributeName="stop-opacity" values="0.7;0.3;0.7" dur="5s" repeatCount="indefinite" />
            </stop>
          </linearGradient>

          <!-- Traditional Mithila Art Patterns -->
          <pattern id="mithila-pattern-{{uniqueId}}" x="0" y="0" width="120" height="120" patternUnits="userSpaceOnUse">
            <!-- Lotus Flower - Central Element in Mithila Art -->
            <g transform="translate(60,60)">
              <!-- Lotus Petals -->
              <g>
                <path d="M0,-25 C10,-20 20,-10 20,0 C20,10 10,20 0,25 C-10,20 -20,10 -20,0 C-20,-10 -10,-20 0,-25Z" 
                      fill="none" [attr.stroke]="'url(#mithila-gradient-' + uniqueId + ')'" stroke-width="0.5">
                  <animate attributeName="stroke-width" values="0.5;1;0.5" dur="4s" repeatCount="indefinite" />
                </path>
                <path d="M25,0 C20,10 10,20 0,20 C-10,20 -20,10 -25,0 C-20,-10 -10,-20 0,-20 C10,-20 20,-10 25,0Z" 
                      fill="none" [attr.stroke]="'url(#mithila-gradient-' + uniqueId + ')'" stroke-width="0.5">
                  <animate attributeName="stroke-width" values="0.5;1;0.5" dur="4s" repeatCount="indefinite" />
                </path>
              </g>
              
              <!-- Center Circle -->
              <circle cx="0" cy="0" r="8" fill="none" [attr.stroke]="'url(#mithila-gradient-' + uniqueId + ')'" stroke-width="0.5">
                <animate attributeName="r" values="8;10;8" dur="3s" repeatCount="indefinite" />
              </circle>
              
              <!-- Decorative Dots - Common in Mithila Art -->
              <g>
                <circle cx="0" cy="-35" r="1.5" [attr.fill]="'url(#mithila-gradient-' + uniqueId + ')'">
                  <animate attributeName="r" values="1.5;2;1.5" dur="2s" repeatCount="indefinite" />
                </circle>
                <circle cx="35" cy="0" r="1.5" [attr.fill]="'url(#mithila-gradient-' + uniqueId + ')'">
                  <animate attributeName="r" values="1.5;2;1.5" dur="2s" repeatCount="indefinite" delay="0.5s" />
                </circle>
                <circle cx="0" cy="35" r="1.5" [attr.fill]="'url(#mithila-gradient-' + uniqueId + ')'">
                  <animate attributeName="r" values="1.5;2;1.5" dur="2s" repeatCount="indefinite" delay="1s" />
                </circle>
                <circle cx="-35" cy="0" r="1.5" [attr.fill]="'url(#mithila-gradient-' + uniqueId + ')'">
                  <animate attributeName="r" values="1.5;2;1.5" dur="2s" repeatCount="indefinite" delay="1.5s" />
                </circle>
              </g>
              
              <!-- Connecting Lines - Geometric Patterns -->
              <g>
                <path d="M0,-8 L0,-25" [attr.stroke]="'url(#mithila-gradient-' + uniqueId + ')'" stroke-width="0.5">
                  <animate attributeName="stroke-width" values="0.5;0.8;0.5" dur="3s" repeatCount="indefinite" />
                </path>
                <path d="M8,0 L25,0" [attr.stroke]="'url(#mithila-gradient-' + uniqueId + ')'" stroke-width="0.5">
                  <animate attributeName="stroke-width" values="0.5;0.8;0.5" dur="3s" repeatCount="indefinite" delay="0.5s" />
                </path>
                <path d="M0,8 L0,25" [attr.stroke]="'url(#mithila-gradient-' + uniqueId + ')'" stroke-width="0.5">
                  <animate attributeName="stroke-width" values="0.5;0.8;0.5" dur="3s" repeatCount="indefinite" delay="1s" />
                </path>
                <path d="M-8,0 L-25,0" [attr.stroke]="'url(#mithila-gradient-' + uniqueId + ')'" stroke-width="0.5">
                  <animate attributeName="stroke-width" values="0.5;0.8;0.5" dur="3s" repeatCount="indefinite" delay="1.5s" />
                </path>
              </g>
            </g>
            
            <!-- Fish Motif - Common in Mithila Art -->
            <g transform="translate(30,30) scale(0.6)">
              <path d="M0,0 C5,-5 15,-5 20,0 C15,5 5,5 0,0Z" fill="none" [attr.stroke]="'url(#mithila-gradient-' + uniqueId + ')'" stroke-width="0.5">
                <animate attributeName="stroke-width" values="0.5;0.8;0.5" dur="3s" repeatCount="indefinite" />
              </path>
              <circle cx="15" cy="0" r="1" [attr.fill]="'url(#mithila-gradient-' + uniqueId + ')'">
                <animate attributeName="r" values="1;1.5;1" dur="2s" repeatCount="indefinite" />
              </circle>
            </g>
            
            <!-- Peacock Motif - Another Common Element -->
            <g transform="translate(90,90) scale(0.6)">
              <path d="M0,0 C5,10 0,15 -5,15 C0,10 0,5 0,0Z" fill="none" [attr.stroke]="'url(#mithila-gradient-' + uniqueId + ')'" stroke-width="0.5">
                <animate attributeName="stroke-width" values="0.5;0.8;0.5" dur="3s" repeatCount="indefinite" />
              </path>
              <path d="M0,0 C-5,10 0,15 5,15 C0,10 0,5 0,0Z" fill="none" [attr.stroke]="'url(#mithila-gradient-' + uniqueId + ')'" stroke-width="0.5">
                <animate attributeName="stroke-width" values="0.5;0.8;0.5" dur="3s" repeatCount="indefinite" delay="0.5s" />
              </path>
              <circle cx="0" cy="0" r="1" [attr.fill]="'url(#mithila-gradient-' + uniqueId + ')'">
                <animate attributeName="r" values="1;1.5;1" dur="2s" repeatCount="indefinite" />
              </circle>
            </g>
          </pattern>
        </defs>
        <rect width="100%" height="100%" [attr.fill]="'url(#mithila-pattern-' + uniqueId + ')'">
          <animate attributeName="opacity" values="0.8;0.4;0.8" dur="8s" repeatCount="indefinite" />
        </rect>
      </svg>
    </div>
  `,
  styles: []
})
export class MithilaArtBackgroundComponent {
  @Input() primaryColor: string = '#C1440E'; // Terracotta Red
  @Input() secondaryColor: string = '#F4B400'; // Sunflower Yellow
  @Input() opacity: string = '20'; // Default opacity 20%
  
  // Generate a unique ID for SVG elements to avoid conflicts when using multiple instances
  uniqueId: string = Math.random().toString(36).substring(2, 9);
}
