import { Component, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-art-card',
  standalone: true,
  imports: [RouterLink, CommonModule],
  templateUrl: './art-card.component.html',
  styleUrl: './art-card.component.css'
})
export class ArtCardComponent {
  @Input() id: string = '';
  @Input() title: string = '';
  @Input() artist: string = '';
  @Input() artistId: string = '';
  @Input() imageUrl: string = '';
  @Input() price: number = 0;
  @Input() medium: string = '';
  @Input() size: string = '';
  @Input() inStock: boolean = true;
}
