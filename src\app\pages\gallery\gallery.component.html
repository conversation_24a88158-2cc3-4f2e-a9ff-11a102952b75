<!-- Enhanced <PERSON> Banner with <PERSON><PERSON><PERSON> Elements -->
<div class="relative h-[60vh] md:h-[70vh] overflow-hidden">
  <!-- Background Image with Parallax Effect -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat transform transition-transform duration-10000 hover:scale-105"
       style="background-image: url('https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg')">
  </div>

  <!-- Animated Gradient Background -->
  <div class="absolute inset-0 overflow-hidden animate-gradient-background">
    <!-- Gradient Overlay -->
    <div class="absolute inset-0 bg-gradient-to-br from-primary-800/90 via-primary-700/90 to-brick-700/90"></div>
    <!-- Animated Gradient Accent -->
    <div class="absolute inset-0 bg-gradient-to-tr from-accent-500/20 via-turmeric-400/10 to-transparent animate-gradient-shift"></div>

    <!-- <PERSON><PERSON><PERSON> Background -->
    <div class="absolute inset-0 opacity-10">
      <app-mithila-art-background
        [primaryColor]="'#C1440E'"
        [secondaryColor]="'#F4B400'"
        opacity="15">
      </app-mithila-art-background>
    </div>

    <!-- Floating Elements -->
    <div class="absolute top-20 right-20 w-32 h-32 rounded-full border border-white/20 opacity-30 animate-float-slow"></div>
    <div class="absolute bottom-20 left-10 w-24 h-24 rounded-full border border-white/20 opacity-20 animate-float-medium"></div>
  </div>

  <!-- Decorative Border -->
  <app-mithila-border
    [primaryColor]="'#C1440E'"
    [secondaryColor]="'#F4B400'"
    [type]="'full'"
    position="top-6 left-6 right-6 bottom-6">
  </app-mithila-border>

  <!-- Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <!-- Decorative Element -->
    <app-mithila-decorative-element
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      [type]="'lotus'"
      position="relative mb-6"
      classes="opacity-90 animate-float-slow"
      size="80px">
    </app-mithila-decorative-element>

    <!-- Title with Animation -->
    <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 font-heading animate-fade-in">
      Art Gallery
    </h1>

    <!-- Decorative Divider -->
    <div class="relative h-1 mx-auto mb-6 w-48 overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-r from-turmeric-300 via-white to-turmeric-300 rounded-full shadow-lg"></div>
      <div class="absolute inset-0 bg-white/30 animate-shimmer"></div>
    </div>

    <!-- Subtitle with Animation -->
    <p class="text-lg sm:text-xl md:text-2xl text-white max-w-3xl mx-auto animate-fade-in-delay">
      Explore our collection of authentic Mithila artwork
    </p>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
      </svg>
    </div>
  </div>
</div>

<!-- Gallery Categories Section -->
<app-mithila-section
  primaryColor="#C1440E"
  secondaryColor="#F4B400"
  backgroundGradient="from-primary-50 via-background-light to-secondary-50"
  backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Explore by Category"
    subtitle="Discover the diverse styles and themes of Mithila art"
  ></app-section-title>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12">
    <div *ngFor="let category of categories; let i = index" class="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
      <!-- Decorative Border -->
      <div class="absolute -inset-1 bg-gradient-to-br rounded-lg blur-sm opacity-30 group-hover:opacity-100 transition-opacity duration-300"
           [ngStyle]="{'background-image': 'linear-gradient(to bottom right, ' + category.color + '80, ' + category.color + '40, ' + category.color + '80)'}">
      </div>

      <div class="relative rounded-lg overflow-hidden">
        <!-- Image -->
        <div class="relative h-64 overflow-hidden">
          <img [src]="category.image" [alt]="category.name" class="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110">

          <!-- Overlay Gradient -->
          <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>

          <!-- Category Name -->
          <div class="absolute inset-0 flex flex-col justify-end p-6">
            <h3 class="text-2xl font-bold text-white mb-2 drop-shadow-md">{{category.name}}</h3>
            <p class="text-white/90 text-sm mb-4 line-clamp-2 drop-shadow-md">{{category.description}}</p>

            <!-- Explore Button -->
            <button (click)="setActiveCategory(category.id)" class="group relative inline-flex items-center px-4 py-2 overflow-hidden rounded-full bg-white/30 backdrop-blur-sm text-white border border-white/50 shadow-lg hover:bg-white hover:text-primary-600 transition-all duration-300 w-fit">
              <span class="relative z-10 flex items-center text-sm font-medium">
                Explore
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </span>
            </button>
          </div>
        </div>
      </div>

      <!-- Decorative Element -->
      <app-mithila-decorative-element
        [primaryColor]="category.color"
        [secondaryColor]="i % 2 === 0 ? '#F4B400' : '#C1440E'"
        [type]="i === 0 ? 'lotus' : i === 1 ? 'peacock' : i === 2 ? 'fish' : 'geometric'"
        position="absolute -bottom-4 -right-4"
        classes="opacity-30 group-hover:opacity-60 transition-opacity duration-300 pointer-events-none"
        size="40px">
      </app-mithila-decorative-element>
    </div>
  </div>
</app-mithila-section>

<!-- Featured Artworks Section -->
<app-mithila-section
  primaryColor="#F4B400"
  secondaryColor="#264653"
  backgroundGradient="from-secondary-50 via-background-light to-accent-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Featured Artworks"
    subtitle="Discover our curated collection of exceptional Mithila art pieces"
  ></app-section-title>

  <!-- Filter Buttons -->
  <div class="flex flex-wrap justify-center gap-3 mt-8 mb-12">
    <button
      (click)="setActiveCategory('all')"
      class="px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 shadow-md"
      [ngClass]="activeCategory === 'all' ? 'bg-primary-600 text-white' : 'bg-white/80 text-gray-700 hover:bg-primary-100'">
      All
    </button>
    <button
      *ngFor="let category of categories"
      (click)="setActiveCategory(category.id)"
      class="px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 shadow-md"
      [ngClass]="activeCategory === category.id ? 'bg-primary-600 text-white' : 'bg-white/80 text-gray-700 hover:bg-primary-100'">
      {{category.name}}
    </button>
  </div>

  <!-- Artwork Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
    <div *ngFor="let artwork of filteredArtworks; let i = index" class="bg-white/80 backdrop-blur-sm rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
      <div class="relative overflow-hidden">
        <!-- Artwork Image -->
        <img [src]="artwork.image" [alt]="artwork.title" class="w-full h-64 object-cover object-center transition-transform duration-700 group-hover:scale-110">

        <!-- Overlay Gradient -->
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <!-- Quick View Button -->
        <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button class="px-6 py-2 bg-white/80 backdrop-blur-sm text-primary-600 rounded-full font-medium transform transition-all duration-300 hover:bg-white hover:scale-105">
            Quick View
          </button>
        </div>
      </div>

      <div class="p-6 relative">
        <!-- Category Badge -->
        <span class="inline-block px-3 py-1 text-xs font-medium rounded-full mb-3"
              [ngClass]="{
                'bg-primary-100 text-primary-600': artwork.category === 'Traditional',
                'bg-secondary-100 text-secondary-600': artwork.category === 'Contemporary',
                'bg-accent-100 text-accent-600': artwork.category === 'Religious',
                'bg-success-100 text-success-600': artwork.category === 'Nature & Wildlife'
              }">
          {{artwork.category}}
        </span>

        <h3 class="text-xl font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{{artwork.title}}</h3>
        <p class="text-primary-600 font-medium">{{artwork.artist}}, {{artwork.year}}</p>
        <p class="text-gray-500 text-sm mt-1">{{artwork.medium}} | {{artwork.dimensions}}</p>
        <p class="text-gray-700 mt-3 line-clamp-2">{{artwork.description}}</p>

        <!-- View Details Link -->
        <a href="#" class="inline-flex items-center mt-4 text-primary-600 font-medium hover:text-primary-700 transition-colors duration-300">
          View Details
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
          </svg>
        </a>

        <!-- Decorative Element -->
        <app-mithila-decorative-element
          [primaryColor]="artwork.category === 'Traditional' ? '#C1440E' :
                          artwork.category === 'Contemporary' ? '#F4B400' :
                          artwork.category === 'Religious' ? '#264653' : '#3B945E'"
          [secondaryColor]="'#E76F51'"
          [type]="i % 4 === 0 ? 'lotus' : i % 4 === 1 ? 'peacock' : i % 4 === 2 ? 'fish' : 'geometric'"
          position="absolute -top-6 -right-6"
          classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"
          size="30px">
        </app-mithila-decorative-element>
      </div>
    </div>
  </div>

  <!-- View All Button -->
  <div class="mt-12 text-center">
    <a href="#" class="group relative inline-flex items-center px-8 py-4 bg-gradient-to-r from-secondary-600 to-primary-600 text-white font-bold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
      <!-- Button Shine Animation -->
      <span class="absolute inset-0 w-full h-full bg-gradient-to-r from-white/0 via-white/20 to-white/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></span>

      <span class="relative z-10 flex items-center">
        <span class="text-lg font-bold drop-shadow">View Full Collection</span>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transform group-hover:translate-x-1 transition-transform duration-300 drop-shadow" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
        </svg>
      </span>
    </a>
  </div>
</app-mithila-section>

<!-- Artist Techniques Section - Unique to Gallery -->
<app-mithila-section
  primaryColor="#264653"
  secondaryColor="#3B945E"
  backgroundGradient="from-accent-50 via-background-light to-success-50"
  backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Traditional Techniques"
    subtitle="Discover the ancient methods and materials that bring Mithila art to life"
  ></app-section-title>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12">
    <!-- Technique 1: Natural Pigments -->
    <div class="mithila-floating-card group text-center">
      <div class="relative overflow-hidden h-48 mb-6">
        <img src="https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg" alt="Natural Pigments" class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>
        <div class="absolute inset-0 flex items-center justify-center">
          <app-mithila-decorative-element
            [primaryColor]="'#C1440E'"
            [secondaryColor]="'#F4B400'"
            [type]="'lotus'"
            position="relative"
            classes="opacity-80"
            size="50px">
          </app-mithila-decorative-element>
        </div>
      </div>
      <h3 class="text-xl font-bold text-gray-900 mb-3">Natural Pigments</h3>
      <p class="text-gray-700 text-sm">Colors derived from flowers, leaves, clay, and minerals create the vibrant palette of Mithila art.</p>
    </div>

    <!-- Technique 2: Bamboo Brushes -->
    <div class="mithila-floating-card group text-center">
      <div class="relative overflow-hidden h-48 mb-6">
        <img src="https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg" alt="Bamboo Brushes" class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>
        <div class="absolute inset-0 flex items-center justify-center">
          <app-mithila-decorative-element
            [primaryColor]="'#F4B400'"
            [secondaryColor]="'#264653'"
            [type]="'peacock'"
            position="relative"
            classes="opacity-80"
            size="50px">
          </app-mithila-decorative-element>
        </div>
      </div>
      <h3 class="text-xl font-bold text-gray-900 mb-3">Bamboo Brushes</h3>
      <p class="text-gray-700 text-sm">Traditional brushes made from bamboo twigs and cotton create the distinctive fine lines and intricate details.</p>
    </div>

    <!-- Technique 3: Handmade Paper -->
    <div class="mithila-floating-card group text-center">
      <div class="relative overflow-hidden h-48 mb-6">
        <img src="https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg" alt="Handmade Paper" class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>
        <div class="absolute inset-0 flex items-center justify-center">
          <app-mithila-decorative-element
            [primaryColor]="'#264653'"
            [secondaryColor]="'#C1440E'"
            [type]="'fish'"
            position="relative"
            classes="opacity-80"
            size="50px">
          </app-mithila-decorative-element>
        </div>
      </div>
      <h3 class="text-xl font-bold text-gray-900 mb-3">Handmade Paper</h3>
      <p class="text-gray-700 text-sm">Locally made paper from natural fibers provides the perfect canvas for traditional Mithila paintings.</p>
    </div>

    <!-- Technique 4: Sacred Geometry -->
    <div class="mithila-floating-card group text-center">
      <div class="relative overflow-hidden h-48 mb-6">
        <img src="https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg" alt="Sacred Geometry" class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>
        <div class="absolute inset-0 flex items-center justify-center">
          <app-mithila-decorative-element
            [primaryColor]="'#3B945E'"
            [secondaryColor]="'#F4B400'"
            [type]="'geometric'"
            position="relative"
            classes="opacity-80"
            size="50px">
          </app-mithila-decorative-element>
        </div>
      </div>
      <h3 class="text-xl font-bold text-gray-900 mb-3">Sacred Geometry</h3>
      <p class="text-gray-700 text-sm">Mathematical precision and spiritual symbolism guide the composition and patterns in every artwork.</p>
    </div>
  </div>

  <!-- Learn More About Techniques -->
  <div class="text-center mt-12">
    <a routerLink="/about" class="inline-block px-8 py-4 bg-accent-600 text-white rounded-lg hover:bg-accent-700 transition-colors duration-300 transform hover:-translate-y-1 hover:shadow-lg">
      <span class="flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
        Learn About Our Heritage
      </span>
    </a>
  </div>
</app-mithila-section>

<!-- Visit Gallery CTA Section -->
<app-mithila-section
  primaryColor="#E76F51"
  secondaryColor="#C1440E"
  backgroundGradient="from-brick-50 via-background-light to-primary-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <div class="max-w-4xl mx-auto">
    <div class="bg-gradient-to-r from-brick-700 to-primary-700 rounded-lg p-8 shadow-lg text-white relative overflow-hidden">
      <!-- Dark Overlay for Better Text Visibility -->
      <div class="absolute inset-0 bg-black/30"></div>

      <!-- Decorative Background Pattern -->
      <div class="absolute inset-0 opacity-5">
        <app-mithila-art-background
          [primaryColor]="'#FFFFFF'"
          [secondaryColor]="'#FFFFFF'"
          opacity="5">
        </app-mithila-art-background>
      </div>

      <div class="relative z-10 text-center">
        <app-mithila-decorative-element
          [primaryColor]="'#E76F51'"
          [secondaryColor]="'#C1440E'"
          [type]="'lotus'"
          position="relative mx-auto mb-6"
          classes="opacity-90"
          size="60px">
        </app-mithila-decorative-element>

        <h2 class="text-3xl font-bold mb-4 text-white drop-shadow-md">Bring Mithila Art Home</h2>
        <p class="mb-8 text-white font-medium text-lg drop-shadow-md max-w-2xl mx-auto">
          Love what you see? Browse our collection of authentic Mithila artwork available for purchase. Each piece comes with a certificate of authenticity and supports the artists directly.
        </p>

        <div class="flex flex-wrap justify-center gap-4">
          <a routerLink="/products" class="group relative inline-flex items-center px-6 py-3 overflow-hidden rounded-full bg-white text-brick-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 text-sm sm:text-base font-bold">
            <!-- Button Shine Animation -->
            <span class="absolute inset-0 w-full h-full bg-gradient-to-r from-brick-100/0 via-brick-100/30 to-brick-100/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></span>

            <span class="relative z-10 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-brick-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
              <span>Shop Collection</span>
            </span>
          </a>

          <a routerLink="/artists" class="group relative inline-flex items-center px-6 py-3 overflow-hidden rounded-full bg-white/50 backdrop-blur-sm text-white border-2 border-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 text-sm sm:text-base font-bold">
            <!-- Button Shine Animation -->
            <span class="absolute inset-0 w-full h-full bg-gradient-to-r from-white/0 via-white/20 to-white/0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></span>

            <span class="relative z-10 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 drop-shadow" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              <span class="font-bold drop-shadow">Meet Artists</span>
            </span>
          </a>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>
