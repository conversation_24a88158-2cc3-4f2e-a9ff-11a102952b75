<div class="card group">
  <div class="relative overflow-hidden">
    <a [routerLink]="['/shop', id]">
      <img [src]="imageUrl" [alt]="title" class="w-full h-64 object-cover object-center transition-transform duration-300 group-hover:scale-105">
    </a>
    <div class="absolute top-2 right-2">
      <span *ngIf="!inStock" class="bg-red-500 text-white text-xs font-semibold px-2 py-1 rounded">Sold Out</span>
    </div>
  </div>
  <div class="p-4">
    <h3 class="text-lg font-semibold text-gray-900 mb-1">
      <a [routerLink]="['/shop', id]" class="hover:text-primary-600 transition-colors duration-200">{{title}}</a>
    </h3>
    <p class="text-sm text-gray-600 mb-2">
      By <a [routerLink]="['/artists', artistId]" class="text-primary-600 hover:underline">{{artist}}</a>
    </p>
    <div class="flex justify-between items-center">
      <div>
        <p class="text-sm text-gray-500">{{medium}} | {{size}}</p>
        <p class="text-lg font-bold text-gray-900">NPR {{price.toLocaleString()}}</p>
      </div>
      <button *ngIf="inStock" class="btn btn-primary text-sm">Add to Cart</button>
    </div>
  </div>
</div>
